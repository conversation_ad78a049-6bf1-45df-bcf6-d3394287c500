import { Blog } from "@/types/blog";
const caseData: Blog[] = [
  {
    id: 1,
    title: "Boosting Ad Performance for Electronics Accessories Sellers with DeepBI",
    paragraph:
      "As an officially certified AI advertising optimization tool, DeepBI achieves a quantum leap in ad performance on Amazon by significantly reducing ACOS and boosting sales revenue through automated keyword addition and ASIN targeting strategies.",
    image: "/images/case/case-01.png",
    author: {
      name: "DeepBI Team",
      image: "/images/blog/author-01.png",
      designation: "AI Specialist",
    },
    tags: ["Cross-border", "Sales", "Smart Ads"],
    publishDate: "2025-09-02",
    content: {
      content: `
        <h3 id="intro">Introduction</h3>
        <p>In Amazon advertising campaigns, AI bidding performance has significantly outperformed manual campaign management! DeepBI is an AI advertising optimization tool with official Amazon SPN certification, backed by official endorsement and pure data sources.</p>
        
        <p>Let's focus on a client case study to witness how DeepBI helped achieve a qualitative leap in advertising performance.</p>
        
        <h3 id="baseline-performance">Baseline Performance Analysis</h3>
        <p>As shown in the chart below, the client's ACOS values were 58.42% and 48.99% one month before using DeepBI.</p>
        
        <img src="/images/case/case-01-detail-01.png" alt="Baseline ACOS Performance" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        
        <h3 id="week-1-results">Week 1: Initial Integration Results</h3>
        <p>After the first week of DeepBI integration, one listing's ACOS immediately performed better than the client's original campaign management, while the other listing showed a slight increase but was already on the optimization trajectory.</p>
        
        <img src="/images/case/case-01-detail-02.png" alt="Week 1 ACOS Results" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        
        <h3 id="week-2-breakthrough">Week 2: Breakthrough Performance</h3>
        <p>By the second week, both listings' ACOS values significantly outperformed the client's original performance. Notably, the initially underperforming listing saw its ACOS plummet from 147.82% to 39.41%. This improvement resulted from DeepBI's initial data exploration phase, with effectiveness typically emerging in the second week.</p>
        
        <img src="/images/case/case-01-detail-03.png" alt="Week 2 ACOS Breakthrough" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        
        <h3 id="strategies-behind-success">Strategies Behind the Success</h3>
        <p>How did the client achieve these results? DeepBI leverages numerous automated optimization strategies behind the scenes. Here are two key strategies:</p>
        
        <ul>
          <li><strong>Automated Keyword Addition Strategy:</strong> Utilizing advanced precision algorithms that follow market trends and consumer search behaviors, precisely adding high-potential keywords to dramatically improve ad relevance and conversion rates.</li>
          <li><strong>Automated ASIN Targeting Strategy:</strong> Automatically identifying ASINs highly relevant to the seller's products and incorporating them into advertising campaigns, further enhancing ad precision.</li>
        </ul>
        
        <h3 id="sales-revenue-growth">Sales Revenue and Impression Growth</h3>
        <p>As shown in the chart below, since implementing DeepBI, the client experienced increased sales revenue and advertising impressions surged by 31.28%. This success stems from DeepBI's "automated keyword addition" and "automated ASIN targeting" strategies, helping sellers quickly identify potential customers and competitors.</p>
        
        <img src="/images/case/case-01-detail-04.png" alt="Sales Revenue and Impression Growth" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        
        <h3 id="sustained-performance">Sustained Performance Excellence</h3>
        <p>From the client's DeepBI activation to present, whether examining the past thirty days or recent seven days, DeepBI's advertising ACOS consistently remained lower than the client's original performance levels. While optimization opportunities still exist, DeepBI's continuous automated optimization capabilities are undeniable.</p>
        
        <img src="/images/case/case-01-detail-05.png" alt="Sustained Performance Excellence" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        
        <h3 id="conclusion">Conclusion: Ready to Transform Your Campaigns?</h3>
        <p>If you're also seeking a truly efficient, hands-off advertising solution, perhaps now is the time to try DeepBI.</p>
      `,
      excerpt: "As an officially certified AI advertising optimization tool, DeepBI achieves a quantum leap in ad performance on Amazon by significantly reducing ACOS and boosting sales revenue through automated keyword addition and ASIN targeting strategies.",
      readingTime: 6,
      sections: [
        {
          id: "intro",
          title: "Introduction",
          content: "<p>In Amazon advertising campaigns, AI bidding performance has significantly outperformed manual campaign management! DeepBI is an AI advertising optimization tool with official Amazon SPN certification, backed by official endorsement and pure data sources.</p>",
          level: 2
        },
        {
          id: "baseline-performance",
          title: "Baseline Performance Analysis",
          content: "<p>As shown in the chart below, the client's ACOS values were 58.42% and 48.99% one month before using DeepBI.</p>",
          level: 2
        },
        {
          id: "week-1-results",
          title: "Week 1: Initial Integration Results",
          content: "<p>After the first week of DeepBI integration, one listing's ACOS immediately performed better than the client's original campaign management, while the other listing showed a slight increase but was already on the optimization trajectory.</p>",
          level: 2
        },
        {
          id: "week-2-breakthrough",
          title: "Week 2: Breakthrough Performance",
          content: "<p>By the second week, both listings' ACOS values significantly outperformed the client's original performance. Notably, the initially underperforming listing saw its ACOS plummet from 147.82% to 39.41%.</p>",
          level: 2
        },
        {
          id: "strategies-behind-success",
          title: "Strategies Behind the Success",
          content: "<p>How did the client achieve these results? DeepBI leverages numerous automated optimization strategies behind the scenes.</p>",
          level: 2
        },
        {
          id: "sales-revenue-growth",
          title: "Sales Revenue and Impression Growth",
          content: "<p>Since implementing DeepBI, the client experienced increased sales revenue and advertising impressions surged by 31.28%.</p>",
          level: 2
        },
        {
          id: "sustained-performance",
          title: "Sustained Performance Excellence",
          content: "<p>From the client's DeepBI activation to present, DeepBI's advertising ACOS consistently remained lower than the client's original performance levels.</p>",
          level: 2
        },
        {
          id: "conclusion",
          title: "Conclusion: Ready to Transform Your Campaigns?",
          content: "<p>If you're also seeking a truly efficient, hands-off advertising solution, perhaps now is the time to try DeepBI.</p>",
          level: 2
        }
      ]
    }
  },
  {
    id: 2,
    title: "DeepBI Empowers Baby Products Sellers with Smarter Ad Optimization",
    paragraph:
      "This case study demonstrates how DeepBI's AI-powered advertising solution helped an Amazon seller reduce ACOS from 155.28% to 42.13% while achieving 46.39% revenue growth through intelligent keyword optimization and budget control strategies.",
    image: "/images/case/case-02.png",
    author: {
      name: "DeepBI Team",
      image: "/images/blog/author-01.png",
      designation: "AI Specialist",
    },
    tags: ["New Ads", "Revenue"],
    publishDate: "2025-09-01",
    content: {
      content: `
        <h3 id="intro">Introduction</h3>
        <p>In the Amazon advertising space, AI-powered campaign management has  surpassed manual optimization. This article presents a real case study  demonstrating how AI advertising campaigns help sellers reduce ACOS  (Advertising Cost of Sales), increase revenue, and provides detailed  analysis of the strategies implemented and their effectiveness.</p>

        <h3 id="acos-optimization">I. Significant ACOS Optimization Results</h3>
        <p>Prior to implementing the DeepBI advertising solution, the client's  ACOS metrics were consistently high, with some campaigns reaching  155.28%.</p>
        <img src="/images/case/case-02-detail-01.png" alt="ACOS before DeepBI" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        <p>However, after adopting DeepBI, ACOS performance improved  dramatically. Through its ACOS control strategy and keyword focus  strategy, DeepBI successfully stabilized ACOS within the merchant's  target range, delivering substantial advertising cost savings while  improving return on ad spend (ROAS).</p>
        <img src="/images/case/case-02-detail-02.png" alt="ACOS after DeepBI" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        <p>ACOS Control Strategy: This strategy centers on  precise advertising budget allocation control. Leveraging DeepBI's  powerful analytics capabilities, we dynamically adjust bidding  strategies based on historical performance data and real-time metrics to  ensure maximum efficiency from every budget dollar. Through granular  bid adjustments in $0.1 increments, we maintain high visibility while  maximizing cost efficiency.</p>
        <p>Keyword Focus Strategy: This approach not only  improved advertising conversion rates but also effectively reduced ACOS.  The system continuously monitors high-cost keyword conversion  performance, ensuring budget concentration on the highest-potential  keywords. When adjusting bids for high-converting keywords, the system  employs a conservative approach with a maximum single adjustment limit  of $2 to prevent unnecessary advertising overspend.</p>
        <h4 id="acos-performance-results">Performance Results:</h4>
        <ul>
          <li>Following DeepBI implementation, ACOS gradually decreased and stabilized</li>
        </ul>
        <img src="/images/case/case-02-detail-03.png" alt="ACOS trend after DeepBI" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        <ul>
          <li>Real-time data from the past 30 days, 7 days, 3 days, and 1 day  shows consistent ACOS optimization, significantly outperforming legacy  campaigns</li>
        </ul>

        <h3 id="revenue-growth">II. Substantial Revenue Growth</h3>
        <ul>
          <li>Through DeepBI's precision targeting and optimization strategies, total advertising revenue increased by 46.39%</li>
          <li>Revenue generated by DeepBI advertising campaigns represents a significant portion of total sales</li>
        </ul>
        <img src="/images/case/case-02-detail-04.png" alt="Revenue growth" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />

        <h3 id="competitor-asin">III. Competitor ASIN Discovery and Advertising Performance Enhancement</h3>
        <p>DeepBI enables sellers to identify highly relevant competitor ASINs,  creating additional advertising exposure opportunities that drive  significant sales growth.</p>
        <p>Competitor ASIN Acquisition and Addition Strategy:  DeepBI intelligently identifies competitor ASINs highly relevant to  client products, expanding advertising reach and visibility.</p>
        <p>Search Term Competitor ASIN Addition Strategy: Through search term report analysis, relevant competitor ASIN keywords are added to expand advertising exposure scope.</p>
        <h4 id="competitor-results">Performance Results:</h4>
        <ul>
          <li>DeepBI generated 641 and 534 ASIN traffic units for the client</li>
        </ul>

        <h3 id="additional-strategies">IV. Additional Intelligent Optimization Strategies</h3>
        <p>DeepBI offers multiple intelligent strategies for comprehensive advertising optimization:</p>
        <ul>
          <li>Automated Keyword Addition Strategy: Intelligently adds high-potential keywords based on market trends and consumer behavior</li>
          <li>Automated ASIN Addition Strategy: Automatically identifies relevant ASINs and incorporates them into advertising campaigns</li>
          <li>Budget Modification Strategy: Real-time budget adjustments based on advertising performance to ensure efficient operation</li>
          <li>Inventory-Budget Adjustment Strategy: Dynamic advertising budget adjustments based on inventory levels to prevent resource waste</li>
        </ul>

        <h3 id="summary">V. Summary</h3>
        <p>In this case study, DeepBI utilized ACOS control and keyword focus  strategies to help the client optimize ACOS from 155.28% to 42.13%  within one month, achieving nearly 300% advertising cost savings and  46.39% revenue growth. With its precise algorithms and efficient  optimization capabilities, DeepBI serves as a powerful tool for Amazon  sellers seeking to enhance advertising effectiveness.</p>
        <img src="/images/case/case-02-detail-05.png" alt="Overall summary chart" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        <p>Whether you're an emerging startup or an established seller focused  on operational excellence, DeepBI can help you discover the optimal  advertising approach for your business. We look forward to partnering  with you to explore your next growth opportunity.</p>
      `,
      excerpt: "In the Amazon advertising space, AI-powered campaign management has  surpassed manual optimization. This article presents a real case study  demonstrating how AI advertising campaigns help sellers reduce ACOS  (Advertising Cost of Sales), increase revenue, and provides detailed  analysis of the strategies implemented and their effectiveness.",
      readingTime: 5,
      sections: [
        { id: "intro", title: "Introduction", content: "<p>In the Amazon advertising space, AI-powered campaign management has  surpassed manual optimization...</p>", level: 2 },
        { id: "acos-optimization", title: "I. Significant ACOS Optimization Results", content: "<p>Prior to implementing the DeepBI advertising solution...</p>", level: 2 },
        { id: "revenue-growth", title: "II. Substantial Revenue Growth", content: "<p>Through DeepBI's precision targeting and optimization strategies...</p>", level: 2 },
        { id: "competitor-asin", title: "III. Competitor ASIN Discovery and Advertising Performance Enhancement", content: "<p>DeepBI enables sellers to identify highly relevant competitor ASINs...</p>", level: 2 },
        { id: "additional-strategies", title: "IV. Additional Intelligent Optimization Strategies", content: "<p>DeepBI offers multiple intelligent strategies for comprehensive advertising optimization...</p>", level: 2 },
        { id: "summary", title: "V. Summary", content: "<p>In this case study, DeepBI utilized ACOS control and keyword focus strategies...</p>", level: 2 },
      ]
    }
  },
  {
    id: 3,
    title: "DeepBI Advertising Optimization for clothing Industry (Winter Cotton Clothing) Sellers",
    paragraph:
      "This case study showcases how DeepBI's intelligent advertising strategies helped a winter clothing seller stabilize ACOS at approximately 10% while achieving 151.20% sales growth and 196.48% impression increase through competitor ASIN discovery and precise keyword optimization.",
    image: "/images/case/case-03.png",
    author: {
      name: "DeepBI Team",
      image: "/images/blog/author-01.png",
      designation: "AI Specialist",
    },
    tags: ["ACOS", "ASIN", "ROI"],
    publishDate: "2025-08-31",
    content: {
      content: `
        <h3 id="intro">Introduction</h3>
        <p>In the Amazon marketplace battleground, do you often find yourself caught  between the dilemma of high sales and low ACOS? This article presents a  real case study demonstrating how DeepBI implements precise strategies  to help sellers break through bottlenecks and achieve a win-win  situation between sales growth and cost control, illuminating the path  for your e-commerce journey!</p>

        <h3 id="acos-optimization">I. ACOS Optimization: DeepBI's Stability and Excellence</h3>
        <p>After implementing DeepBI, this customer's ACOS stabilized within the  optimal range of approximately 10%, achieving high return on investment  and consistent advertising performance.</p>
        <p><strong>ACOS Control Strategy:</strong> The core advantage of this strategy lies in  the precise management of advertising budget allocation. Leveraging  DeepBI's deep analytical capabilities, it dynamically adjusts bidding  strategies based on historical data and current performance, ensuring  that every dollar of budget generates maximum returns. Through  fine-tuning bids with precision down to $0.1 increments, it strives to  maximize cost efficiency while maintaining high advertising visibility.</p>
        <p><strong>High-Performance Keyword Strategy:</strong> This strategy not only  significantly improved advertising conversion rates but also effectively  reduced ACOS. The system tracks and analyzes the conversion performance  of high-cost keywords in real-time, ensuring advertising budget is  precisely focused on the most promising keywords. When adjusting bids  for high-converting keywords, the system adopts a conservative approach  with single bid adjustments not exceeding $2, preventing unnecessary  increases in advertising costs.</p>
        <h4 id="acos-results">Performance Results:</h4>
        <ul>
          <li>The ACOS trend chart for the most recent week demonstrates DeepBI's stable and exceptional performance.</li>
        </ul>
        <img src="/images/case/case-03-detail-01.png" alt="Weekly ACOS trend" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />

        <h3 id="sales-growth">II. Significant Sales Growth</h3>
        <p>Sales generated from DeepBI advertising campaigns account for a substantial portion, with some reaching 100%.</p>
        <img src="/images/case/case-03-detail-02.png" alt="Sales contribution from DeepBI" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        <h4 id="data-highlights">Data Highlights:</h4>
        <ul>
          <li>After the customer implemented DeepBI on December 17th, total advertising sales increased by over 151.20%.</li>
          <li>Precise targeting and optimization strategies enhanced product visibility.</li>
        </ul>
        <img src="/images/case/case-03-detail-03.png" alt="Sales growth highlight" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        <img src="/images/case/case-03-detail-04.png" alt="Visibility improvement highlight" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />

        <h3 id="competitor-asin">III. Competitor ASIN Discovery and Advertising Performance Enhancement</h3>
        <p>Through its competitor ASIN discovery strategy, DeepBI precisely  identified numerous relevant competitor ASINs for the customer and  incorporated them into advertising campaigns, significantly boosting  advertising impressions and creating more sales opportunities.</p>
        <ol>
          <li><strong>Competitor ASIN Acquisition Strategy:</strong> DeepBI intelligently  identifies competitor ASINs highly relevant to the customer's products,  expanding advertising reach.</li>
          <li><strong>Search Term Competitor ASIN Addition Strategy:</strong> By analyzing search  term reports, relevant competitor ASIN keywords are added to expand  advertising exposure.</li>
        </ol>
        <h4 id="competitor-results">Performance Results:</h4>
        <ul>
          <li>DeepBI generated over 1,000 ASIN traffic units for the customer.</li>
        </ul>
        <img src="/images/case/case-03-detail-05.png" alt="ASIN traffic units generated" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        <p>Advertising impressions increased by 196.48%, successfully capturing competitor traffic.</p>

        <h3 id="additional-features">IV. DeepBI's Additional Powerful Features</h3>
        <p>DeepBI offers various intelligent strategies for comprehensive advertising optimization:</p>
        <ul>
          <li>Automatic Keyword Addition Strategy: Intelligently adds high-potential keywords based on market trends and consumer behavior.</li>
          <li>Automatic ASIN Addition Strategy: Automatically identifies relevant ASINs and incorporates them into advertising plans.</li>
          <li>SKU Pause Strategy: Automatically pauses related SKU advertisements  when product inventory is insufficient or products are about to be  delisted, avoiding ineffective spending.</li>
        </ul>

        <h3 id="summary">V. Summary</h3>
        <p>In this case study, DeepBI helped the Mexico marketplace customer  achieve a win-win situation between ACOS and sales through ACOS control  strategy, high-performance keyword strategy, and competitor ASIN  discovery strategy:</p>
        <ul>
          <li>ACOS stabilized at approximately 10%.</li>
        </ul>
        <img src="/images/case/case-03-detail-06.png" alt="ACOS stabilized around 10%" class="w-full max-w-sm mx-auto rounded-xl shadow-lg mb-8" />
        <ul>
          <li>Total advertising sales increased by 151.20%, with advertising impressions rising by nearly 196.48%.</li>
        </ul>
        <p>DeepBI is not a templated tool, but rather a truly intelligent  advertising system that adapts to needs and continuously optimizes. The  next breakthrough achiever could be you.</p>
      `,
      excerpt: "This case study showcases how DeepBI's intelligent advertising strategies helped a winter clothing seller stabilize ACOS at approximately 10% while achieving 151.20% sales growth and 196.48% impression increase through competitor ASIN discovery and precise keyword optimization.",
      readingTime: 5,
      sections: [
        { id: "intro", title: "Introduction", content: "<p>Overview and context.</p>", level: 2 },
        { id: "acos-optimization", title: "I. ACOS Optimization: DeepBI's Stability and Excellence", content: "<p>ACOS control + high-performance keywords.</p>", level: 2 },
        { id: "sales-growth", title: "II. Significant Sales Growth", content: "<p>Sales contribution and data highlights.</p>", level: 2 },
        { id: "competitor-asin", title: "III. Competitor ASIN Discovery and Advertising Performance Enhancement", content: "<p>ASIN acquisition and search-term addition strategies.</p>", level: 2 },
        { id: "additional-features", title: "IV. DeepBI's Additional Powerful Features", content: "<p>Keyword/ASIN automation, SKU pause.</p>", level: 2 },
        { id: "summary", title: "V. Summary", content: "<p>ACOS ~10%, +151.20% sales, +196.48% impressions.</p>", level: 2 }
      ]
    }
  },
];

export default caseData;


