import { Blog } from "@/types/blog";
const caseData: Blog[] = [
  {
    id: 1,
    title: "Boosting Ad Performance for Electronics Accessories Sellers with DeepBI",
    paragraph:
      "As an officially certified AI advertising optimization tool, DeepBI achieves a quantum leap in ad performance on Amazon by significantly reducing ACOS and boosting sales revenue through automated keyword addition and ASIN targeting strategies.",
    image: "/images/case/case-01.png",
    author: {
      name: "DeepBI Team",
      image: "/images/blog/author-01.png",
      designation: "AI Specialist",
    },
    tags: ["Cross-border", "Sales", "Smart Ads"],
    publishDate: "2025-09-02",
    content: {
      content: `
        <h3 id="intro">Introduction</h3>
        <p>In Amazon advertising campaigns, AI bidding performance has significantly outperformed manual campaign management! DeepBI is an AI advertising optimization tool with official Amazon SPN certification, backed by official endorsement and pure data sources.</p>
        
        <p>Let's focus on a client case study to witness how DeepBI helped achieve a qualitative leap in advertising performance.</p>
        
        <h3 id="baseline-performance">Baseline Performance Analysis</h3>
        <p>As shown in the chart below, the client's ACOS values were 58.42% and 48.99% one month before using DeepBI.</p>
        
        <img src="/images/case/case-01-detail-01.png" alt="Baseline ACOS Performance" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        
        <h3 id="week-1-results">Week 1: Initial Integration Results</h3>
        <p>After the first week of DeepBI integration, one listing's ACOS immediately performed better than the client's original campaign management, while the other listing showed a slight increase but was already on the optimization trajectory.</p>
        
        <img src="/images/case/case-01-detail-02.png" alt="Week 1 ACOS Results" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        
        <h3 id="week-2-breakthrough">Week 2: Breakthrough Performance</h3>
        <p>By the second week, both listings' ACOS values significantly outperformed the client's original performance. Notably, the initially underperforming listing saw its ACOS plummet from 147.82% to 39.41%. This improvement resulted from DeepBI's initial data exploration phase, with effectiveness typically emerging in the second week.</p>
        
        <img src="/images/case/case-01-detail-03.png" alt="Week 2 ACOS Breakthrough" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        
        <h3 id="strategies-behind-success">Strategies Behind the Success</h3>
        <p>How did the client achieve these results? DeepBI leverages numerous automated optimization strategies behind the scenes. Here are two key strategies:</p>
        
        <ul>
          <li><strong>Automated Keyword Addition Strategy:</strong> Utilizing advanced precision algorithms that follow market trends and consumer search behaviors, precisely adding high-potential keywords to dramatically improve ad relevance and conversion rates.</li>
          <li><strong>Automated ASIN Targeting Strategy:</strong> Automatically identifying ASINs highly relevant to the seller's products and incorporating them into advertising campaigns, further enhancing ad precision.</li>
        </ul>
        
        <h3 id="sales-revenue-growth">Sales Revenue and Impression Growth</h3>
        <p>As shown in the chart below, since implementing DeepBI, the client experienced increased sales revenue and advertising impressions surged by 31.28%. This success stems from DeepBI's "automated keyword addition" and "automated ASIN targeting" strategies, helping sellers quickly identify potential customers and competitors.</p>
        
        <img src="/images/case/case-01-detail-04.png" alt="Sales Revenue and Impression Growth" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        
        <h3 id="sustained-performance">Sustained Performance Excellence</h3>
        <p>From the client's DeepBI activation to present, whether examining the past thirty days or recent seven days, DeepBI's advertising ACOS consistently remained lower than the client's original performance levels. While optimization opportunities still exist, DeepBI's continuous automated optimization capabilities are undeniable.</p>
        
        <img src="/images/case/case-01-detail-05.png" alt="Sustained Performance Excellence" class="w-full max-w-4xl mx-auto rounded-xl shadow-lg mb-8" />
        
        <h3 id="conclusion">Conclusion: Ready to Transform Your Campaigns?</h3>
        <p>If you're also seeking a truly efficient, hands-off advertising solution, perhaps now is the time to try DeepBI.</p>
      `,
      excerpt: "As an officially certified AI advertising optimization tool, DeepBI achieves a quantum leap in ad performance on Amazon by significantly reducing ACOS and boosting sales revenue through automated keyword addition and ASIN targeting strategies.",
      readingTime: 5,
      sections: [
        {
          id: "intro",
          title: "Introduction",
          content: "<p>In Amazon advertising campaigns, AI bidding performance has significantly outperformed manual campaign management! DeepBI is an AI advertising optimization tool with official Amazon SPN certification, backed by official endorsement and pure data sources.</p>",
          level: 2
        },
        {
          id: "baseline-performance",
          title: "Baseline Performance Analysis",
          content: "<p>As shown in the chart below, the client's ACOS values were 58.42% and 48.99% one month before using DeepBI.</p>",
          level: 2
        },
        {
          id: "week-1-results",
          title: "Week 1: Initial Integration Results",
          content: "<p>After the first week of DeepBI integration, one listing's ACOS immediately performed better than the client's original campaign management, while the other listing showed a slight increase but was already on the optimization trajectory.</p>",
          level: 2
        },
        {
          id: "week-2-breakthrough",
          title: "Week 2: Breakthrough Performance",
          content: "<p>By the second week, both listings' ACOS values significantly outperformed the client's original performance. Notably, the initially underperforming listing saw its ACOS plummet from 147.82% to 39.41%.</p>",
          level: 2
        },
        {
          id: "strategies-behind-success",
          title: "Strategies Behind the Success",
          content: "<p>How did the client achieve these results? DeepBI leverages numerous automated optimization strategies behind the scenes.</p>",
          level: 2
        },
        {
          id: "sales-revenue-growth",
          title: "Sales Revenue and Impression Growth",
          content: "<p>Since implementing DeepBI, the client experienced increased sales revenue and advertising impressions surged by 31.28%.</p>",
          level: 2
        },
        {
          id: "sustained-performance",
          title: "Sustained Performance Excellence",
          content: "<p>From the client's DeepBI activation to present, DeepBI's advertising ACOS consistently remained lower than the client's original performance levels.</p>",
          level: 2
        },
        {
          id: "conclusion",
          title: "Conclusion: Ready to Transform Your Campaigns?",
          content: "<p>If you're also seeking a truly efficient, hands-off advertising solution, perhaps now is the time to try DeepBI.</p>",
          level: 2
        }
      ]
    }
  },
];

export default caseData;


