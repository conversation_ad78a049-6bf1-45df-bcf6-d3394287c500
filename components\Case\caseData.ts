import { CaseItem } from "@/types/case";

const caseData: CaseItem[] = [
  {
    id: 1,
    title: "Case Study: 3C Seller Reduces ACOS by 32% in 3 Weeks",
    paragraph:
      "Through AI-powered bid optimization and keyword management, ACOS was reduced from 28% to 19% while increasing overall sales by 15%",
    image: "/images/blog/post-01.jpg",
    author: {
      name: "DeepBI Team",
      image: "/images/blog/author-01.png",
      designation: "Growth Analyst",
    },
    tags: ["ROI", "ACOS", "Automation"],
    publishDate: "2024-08-15",
    content: {
      content: `
        <h2>Project Background</h2>
        <p>The client is a 3C category seller with high advertising spend but consistently high ACOS. The goal was to reduce ACOS while maintaining sales volume.</p>
        <h2>Solution</h2>
        <ul>
          <li>AI-powered bid optimization strategy</li>
          <li>Keyword segmentation and negative keyword strategy</li>
          <li>Budget allocation and timing optimization</li>
        </ul>
        <h2>Results</h2>
        <p>ACOS decreased by 32% in three weeks, CTR improved by 12%, conversion rate increased by 9%, and total sales grew by 15%.</p>
      `,
      excerpt: "AI bid optimization and keyword management reduced ACOS from 28% to 19% while increasing sales by 15%",
      readingTime: 5,
      status: 'published',
      publishDate: "2024-08-15",
      sections: [
        { id: "background", title: "Project Background", content: "<p>The client is a 3C category seller...</p>", level: 2 },
        { id: "solution", title: "Solution", content: "<p>AI-powered bid optimization...</p>", level: 2 },
        { id: "results", title: "Results", content: "<p>ACOS decreased by 32%...</p>", level: 2 },
      ],
    },
  },
];

export default caseData;


