'use client';

import React, { useState, Suspense, useEffect } from 'react';
import Image from 'next/image';
import { Icon } from '@iconify/react';
import AOS from 'aos';
import EmailSubscription from '@/components/EmailSubscription';

const HomePage = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isPc, setIsPc] = useState(true);

  // Try It Now 按钮点击处理
  const handleTryItNowClick = () => {
    const element = document.getElementById('try-it-now');
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  const carouselSlides = [
    { id: 0, image: '/images/Home/banner1.png' },
    { id: 1, image: '/images/Home/banner2.png' },
    { id: 2, image: '/images/Home/banner3.jpg' },
    { id: 3, image: '/images/Home/banner4.jpg' }
  ];

  useEffect(() => {
    // 判断是否是pc
    const userAgent = navigator.userAgent;
    const isMobile = /Mobile|webOS|BlackBerry|IEMobile|MeeGo|mini|Fennec|Android|iP(ad|od|hone)/i.test(userAgent);
    setIsPc(!isMobile);
  }, []);

  useEffect(() => {
    // 初始化AOS动画库
    AOS.init({
      duration: 800,
      once: false,
      mirror: true,
      offset: 50,
    });
  }, []);

  // 移动端Banner幻灯片
  const mobileBannerSlides = [
    { id: 2, image: '/images/Home/bg/banner2.jpg' },
    { id: 3, image: '/images/Home/bg/banner3.png' },
  ];
  // PC端Banner幻灯片
  const pcBannerSlides = [
    { id: 1, image: '/images/Home/bg/banner1.png' },
    { id: 3, image: '/images/Home/bg/banner3.png' },
  ];
  // 根据设备类型选择要使用的slides
  const bannerSlides = isPc ? pcBannerSlides : mobileBannerSlides;

  const features = [
    {
      title: 'Smart Ad Optimization',
      description: 'Select your ASINs. DeepBI creates, launches, and optimizes your ads. No setup needed—just pick your products, and we&apos;ll handle the rest.',
      icon: 'mdi:cog-outline',
      image: '/images/svg/features/smart-ad-optimization.svg',
    },
    {
      title: 'Keyword & Competitor Targeting',
      description: 'Smartly mine long-tail keywords and competitor ASINs for high-intent traffic. Stop guessing, and reach customers who are ready to buy.',
      icon: 'mdi:target-arrow',
      image: '/images/svg/features/keyword-targeting.svg',
    },
    {
      title: 'ACOS Lowering Engine',
      description: 'Set your ACoS goal. DeepBI will adjust everything to hit it. Intelligent ACoS control for more precise ad targeting.',
      icon: 'mdi:account-cash-outline',
      image: '/images/svg/features/acos-lowering.svg',
    },
    {
      title: 'Performance Comparison',
      description: 'Compare new campaigns with your original ones to see the AI in action. Real-time performance insights show you exactly what works.',
      icon: 'mdi:chart-bar',
      image: '/images/svg/features/performance-comparison.svg',
    },
    {
      title: 'Auto Budget Optimization',
      description: 'Shift your budget toward top-performing campaigns. Prioritize high-ROI ads, and automatically throttle low-performing ones. Never waste your ad budget again.',
      icon: 'mdi:trending-up',
      image: '/images/svg/features/auto-budget-optimization.svg',
    },
  ];

  const [selectedFeatureIndex, setSelectedFeatureIndex] = useState(0);

  return (
    <main className="bg-white">
      <style jsx global>{`
        @keyframes pulse-soft {
          0%, 100% { opacity: 0.9; }
          50% { opacity: 1; }
        }
        .animate-pulse-soft {
          animation: pulse-soft 3s infinite ease-in-out;
        }
        
        @keyframes pulse-slow {
          0%, 100% { opacity: 0.7; }
          50% { opacity: 0.9; }
        }
        .animate-pulse-slow {
          animation: pulse-slow 6s infinite ease-in-out;
        }
        
        @keyframes float {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-10px); }
        }
        .animate-float {
          animation: float 5s infinite ease-in-out;
        }
        
        @keyframes float-slow {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-15px); }
        }
        .animate-float-slow {
          animation: float-slow 8s infinite ease-in-out;
        }
        
        @keyframes bounce-subtle {
          0%, 100% { transform: translateY(0); }
          50% { transform: translateY(-3px); }
        }
        .animate-bounce-subtle {
          animation: bounce-subtle 2s infinite ease-in-out;
        }
        
        .text-shadow-lg {
          text-shadow: 0 4px 12px rgba(0, 0, 0, 0.8);
        }
        
        .typing-cursor::after {
          content: '|';
          margin-left: 2px;
          animation: blink 1s step-end infinite;
        }
        
        @keyframes blink {
          from, to { opacity: 1; }
          50% { opacity: 0; }
        }
      `}</style>
      
      {/* Hero Section */}
      <section id="home" className="relative z-10 overflow-hidden bg-white min-h-screen flex items-center justify-center dark:bg-gray-dark">
        <div className="container">
          <div className="-mx-4 flex flex-wrap">
            <div className="w-full px-4">
              <div className="mx-auto max-w-[800px] lg:max-w-[1200px] text-center">
                {/* 副标题徽章 */}
                <div className="inline-flex items-center px-4 py-2 bg-green-100 rounded-full text-green-800 text-sm font-medium mb-8">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                  Amazon SPN provider
        </div>

                {/* 主标题 */}
                <h1 className="mb-5 text-xl font-bold leading-tight text-black dark:text-white sm:text-2xl md:text-3xl lg:text-5xl font-heading">
                DeepBI – Your AI-Powered Amazon Ad Assistant
                </h1>
                
                {/* 描述文字 */}
                <p className="mb-12 text-xs leading-relaxed text-body-color dark:text-body-color-dark sm:text-sm md:text-base lg:text-lg">
                  One-click AI Automation, Lower ACOS, Better Sales
                  {/* The AI assistant helps you run and optimize Amazon PPC campaigns without hiring expensive agencies. */}
                </p>
                
                {/* 按钮组 */}
                <div className="flex flex-col items-center justify-center space-y-3 sm:flex-row sm:space-x-4 sm:space-y-0">
                  <button
                    onClick={handleTryItNowClick}
                    className="w-full sm:w-auto rounded-lg bg-blue-600 px-6 py-3 sm:px-8 sm:py-4 text-sm sm:text-base font-semibold text-white duration-300 ease-in-out hover:bg-blue-700 font-heading"
                  >
                    🚀 Try It Now
                </button>
                  {/* <button className="w-full sm:w-auto inline-block rounded-lg bg-black px-6 py-3 sm:px-8 sm:py-4 text-sm sm:text-base font-semibold text-white duration-300 ease-in-out hover:bg-black/90 dark:bg-white/10 dark:text-white dark:hover:bg-white/5 font-heading">
                  Book a Demo
              </button> */}
            </div>

                {/* 统计数据 */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-8 mt-12 sm:mt-16">
                  <div className="text-center">
                    <div className="text-2xl sm:text-3xl font-bold text-red-600 mb-2">32%</div>
                    <div className="text-gray-600 text-xs sm:text-sm font-medium">Average ACOS Reduction</div>
                </div>
                  <div className="text-center">
                    <div className="text-2xl sm:text-3xl font-bold text-green-600 mb-2">47%</div>
                    <div className="text-gray-600 text-xs sm:text-sm font-medium">Sales Growth in 90 Days</div>
              </div>
                  <div className="text-center">
                    <div className="text-2xl sm:text-3xl font-bold text-blue-600 mb-2">85%</div>
                    <div className="text-gray-600 text-xs sm:text-sm font-medium">Time Saved</div>
            </div>
          </div>
        </div>
                </div>
              </div>
            </div>
            
        {/* 装饰性SVG - 右上角 */}
        <div className="absolute right-0 top-0 z-[-1] opacity-30 lg:opacity-100">
          <svg
            width="450"
            height="556"
            viewBox="0 0 450 556"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="277"
              cy="63"
              r="225"
              fill="url(#paint0_linear_25:217)"
            />
            <circle
              cx="17.9997"
              cy="182"
              r="18"
              fill="url(#paint1_radial_25:217)"
            />
            <circle
              cx="76.9997"
              cy="288"
              r="34"
              fill="url(#paint2_radial_25:217)"
            />
            <circle
              cx="325.486"
              cy="302.87"
              r="180"
              transform="rotate(-37.6852 325.486 302.87)"
              fill="url(#paint3_linear_25:217)"
            />
            <circle
              opacity="0.8"
              cx="184.521"
              cy="315.521"
              r="132.862"
              transform="rotate(114.874 184.521 315.521)"
              stroke="url(#paint4_linear_25:217)"
            />
            <circle
              opacity="0.8"
              cx="356"
              cy="290"
              r="179.5"
              transform="rotate(-30 356 290)"
              stroke="url(#paint5_linear_25:217)"
            />
            <circle
              opacity="0.8"
              cx="191.659"
              cy="302.659"
              r="133.362"
              transform="rotate(133.319 191.659 302.659)"
              fill="url(#paint6_linear_25:217)"
            />
            <defs>
              <linearGradient
                id="paint0_linear_25:217"
                x1="-54.5003"
                y1="-178"
                x2="222"
                y2="288"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </linearGradient>
              <radialGradient
                id="paint1_radial_25:217"
                cx="0"
                cy="0"
                r="1"
                gradientUnits="userSpaceOnUse"
                gradientTransform="translate(17.9997 182) rotate(90) scale(18)"
              >
                <stop offset="0.145833" stopColor="#4A6CF7" stopOpacity="0" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0.08" />
              </radialGradient>
              <radialGradient
                id="paint2_radial_25:217"
                cx="0"
                cy="0"
                r="1"
                gradientUnits="userSpaceOnUse"
                gradientTransform="translate(76.9997 288) rotate(90) scale(34)"
              >
                <stop offset="0.145833" stopColor="#4A6CF7" stopOpacity="0" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0.08" />
              </radialGradient>
              <linearGradient
                id="paint3_linear_25:217"
                x1="226.775"
                y1="-66.1548"
                x2="292.157"
                y2="351.421"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </linearGradient>
              <linearGradient
                id="paint4_linear_25:217"
                x1="184.521"
                y1="182.159"
                x2="184.521"
                y2="448.882"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="white" stopOpacity="0" />
              </linearGradient>
              <linearGradient
                id="paint5_linear_25:217"
                x1="356"
                y1="110"
                x2="356"
                y2="470"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="white" stopOpacity="0" />
              </linearGradient>
              <linearGradient
                id="paint6_linear_25:217"
                x1="118.524"
                y1="29.2497"
                x2="166.965"
                y2="338.63"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </linearGradient>
            </defs>
          </svg>
                </div>
        
        {/* 装饰性SVG - 左下角 */}
        <div className="absolute bottom-0 left-0 z-[-1] opacity-30 lg:opacity-100">
          <svg
            width="364"
            height="201"
            viewBox="0 0 364 201"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M5.88928 72.3303C33.6599 66.4798 101.397 64.9086 150.178 105.427C211.155 156.076 229.59 162.093 264.333 166.607C299.076 171.12 337.718 183.657 362.889 212.24"
              stroke="url(#paint0_linear_25:218)"
            />
            <path
              d="M-22.1107 72.3303C5.65989 66.4798 73.3965 64.9086 122.178 105.427C183.155 156.076 201.59 162.093 236.333 166.607C271.076 171.12 309.718 183.657 334.889 212.24"
              stroke="url(#paint1_linear_25:218)"
            />
            <path
              d="M-53.1107 72.3303C-25.3401 66.4798 42.3965 64.9086 91.1783 105.427C152.155 156.076 170.59 162.093 205.333 166.607C240.076 171.12 278.718 183.657 303.889 212.24"
              stroke="url(#paint2_linear_25:218)"
            />
            <path
              d="M-98.1618 65.0889C-68.1416 60.0601 4.73364 60.4882 56.0734 102.431C120.248 154.86 139.905 161.419 177.137 166.956C214.37 172.493 255.575 186.165 281.856 215.481"
              stroke="url(#paint3_linear_25:218)"
            />
            <circle
              opacity="0.8"
              cx="214.505"
              cy="60.5054"
              r="49.7205"
              transform="rotate(-13.421 214.505 60.5054)"
              stroke="url(#paint4_linear_25:218)"
            />
            <circle cx="220" cy="63" r="43" fill="url(#paint5_radial_25:218)" />
            <defs>
              <linearGradient
                id="paint0_linear_25:218"
                x1="184.389"
                y1="69.2405"
                x2="184.389"
                y2="212.24"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" stopOpacity="0" />
                <stop offset="1" stopColor="#4A6CF7" />
              </linearGradient>
              <linearGradient
                id="paint1_linear_25:218"
                x1="156.389"
                y1="69.2405"
                x2="156.389"
                y2="212.24"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" stopOpacity="0" />
                <stop offset="1" stopColor="#4A6CF7" />
              </linearGradient>
              <linearGradient
                id="paint2_linear_25:218"
                x1="125.389"
                y1="69.2405"
                x2="125.389"
                y2="212.24"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" stopOpacity="0" />
                <stop offset="1" stopColor="#4A6CF7" />
              </linearGradient>
              <linearGradient
                id="paint3_linear_25:218"
                x1="93.8507"
                y1="67.2674"
                x2="89.9278"
                y2="210.214"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" stopOpacity="0" />
                <stop offset="1" stopColor="#4A6CF7" />
              </linearGradient>
              <linearGradient
                id="paint4_linear_25:218"
                x1="214.505"
                y1="10.2849"
                x2="212.684"
                y2="99.5816"
                gradientUnits="userSpaceOnUse"
              >
                <stop stopColor="#4A6CF7" />
                <stop offset="1" stopColor="#4A6CF7" stopOpacity="0" />
              </linearGradient>
              <radialGradient
                id="paint5_radial_25:218"
                cx="0"
                cy="0"
                r="1"
                gradientUnits="userSpaceOnUse"
                gradientTransform="translate(220 63) rotate(90) scale(43)"
              >
                <stop offset="0.145833" stopColor="white" stopOpacity="0" />
                <stop offset="1" stopColor="white" stopOpacity="0.08" />
              </radialGradient>
            </defs>
          </svg>
              </div>
      </section>

      {/* Who it's for Section */}
      <section id="who-its-for" className="py-20 md:py-32 px-4 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full blur-3xl opacity-30"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-100 rounded-full blur-3xl opacity-30"></div>
            </div>
            
        <div className="max-w-7xl mx-auto relative z-10" data-aos="fade-up">
          <div className="text-center mb-12 lg:mb-16">
            <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6 text-gray-900">
              Who It&apos;s <span className="text-blue-600">For</span>
            </h2>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed px-4">
              Built for modern Amazon sellers in different stages of growth
            </p>
                </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6 px-4">
            <div className="bg-white p-4 sm:p-6 rounded-xl border border-gray-200 hover:border-green-300 hover:shadow-md transition-all duration-300" data-aos="fade-up" data-aos-delay="100">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center mb-3 sm:mb-4">
                <Icon icon="mdi:store" className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-sm sm:text-base text-gray-900 mb-2 sm:mb-3">Solo Sellers</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
              Not an ad expert? DeepBI automates campaigns and saves you hours every week. 
              </p>
            </div>
            
            <div className="bg-white p-4 sm:p-6 rounded-xl border border-gray-200 hover:border-purple-300 hover:shadow-md transition-all duration-300" data-aos="fade-up" data-aos-delay="200">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-3 sm:mb-4">
                <Icon icon="mdi:account-group" className="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" />
                </div>
              <h3 className="font-semibold text-sm sm:text-base text-gray-900 mb-2 sm:mb-3">Small Teams</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
              Managing 10+ ASINs or marketplaces? DeepBI supports large-scale execution with ease.
              </p>
            </div>
            
            <div className="bg-white p-4 sm:p-6 rounded-xl border border-gray-200 hover:border-orange-300 hover:shadow-md transition-all duration-300" data-aos="fade-up" data-aos-delay="300">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-3 sm:mb-4">
                <Icon icon="mdi:rocket-launch" className="w-5 h-5 sm:w-6 sm:h-6 text-orange-600" />
                </div>
              <h3 className="font-semibold text-sm sm:text-base text-gray-900 mb-2 sm:mb-3">New Product Launches</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
              Launching a new product? DeepBI helps explore keywords and competitor traffic efficiently. 
              </p>
              </div>
            
            <div className="bg-white p-4 sm:p-6 rounded-xl border border-gray-200 hover:border-cyan-300 hover:shadow-md transition-all duration-300" data-aos="fade-up" data-aos-delay="400">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-cyan-100 rounded-lg flex items-center justify-center mb-3 sm:mb-4">
                <Icon icon="mdi:chart-line" className="w-5 h-5 sm:w-6 sm:h-6 text-cyan-600" />
              </div>
              <h3 className="font-semibold text-sm sm:text-base text-gray-900 mb-2 sm:mb-3">Cross-Market Operations</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
              Running ads in multiple countries? DeepBI adapts to each market&apos;s performance — like having a skilled optimizer for every region, without growing your team or budget. 
              </p>
            </div>
            
            <div className="bg-white p-4 sm:p-6 rounded-xl border border-gray-200 hover:border-red-300 hover:shadow-md transition-all duration-300" data-aos="fade-up" data-aos-delay="500">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-red-100 rounded-lg flex items-center justify-center mb-3 sm:mb-4">
                <Icon icon="mdi:cash" className="w-5 h-5 sm:w-6 sm:h-6 text-red-600" />
                </div>
              <h3 className="font-semibold text-sm sm:text-base text-gray-900 mb-2 sm:mb-3">Performance-Driven Pros</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
              Running at full capacity? DeepBI expands keyword &amp; ASIN coverage beyond what’s possible manually — with daily smart optimizations. 
              </p>
              </div>
            
            <div className="bg-white p-4 sm:p-6 rounded-xl border border-gray-200 hover:border-yellow-300 hover:shadow-md transition-all duration-300" data-aos="fade-up" data-aos-delay="600">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-3 sm:mb-4">
                <Icon icon="mdi:poll" className="w-5 h-5 sm:w-6 sm:h-6 text-yellow-600" />
              </div>
              <h3 className="font-semibold text-sm sm:text-base text-gray-900 mb-2 sm:mb-3">Growth and Scale-Minded Marketers</h3>
              <p className="text-gray-600 text-sm leading-relaxed">
              Let DeepBI&apos;s AI find growth opportunities while maintaining your target ACOS and profitability levels.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Why Sellers Love DeepBI */}
      <section id="why-sellers-love" className="py-20 md:py-32 px-4 relative bg-white overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-50 rounded-full blur-3xl opacity-60"></div>
          <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-purple-50 rounded-full blur-3xl opacity-60"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10" data-aos="fade-up">
          <div className="text-center mb-12 lg:mb-16">
            <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6 text-gray-900">
              Why Sellers <span className="text-blue-600">Love</span> DeepBI
            </h2>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed px-4">
              More Than Just Time-Saving: Smarter, Scalable, Structured Gains
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
            <div className="space-y-8" data-aos="fade-right">
                <div className="flex items-start">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                    <Icon icon="mdi:check" className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                  <h3 className="font-semibold text-lg sm:text-xl text-gray-900 mb-2">Data-Driven Decisions</h3>
                    <p className="text-gray-600 text-sm sm:text-base">No more guesswork. DeepBI relies on actual performance data, not gut feelings.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                  <Icon icon="mdi:check" className="w-5 h-5 text-green-600" />
                  </div>
                  <div>
                  <h3 className="font-semibold text-lg sm:text-xl text-gray-900 mb-2">Smarter Budget Allocation</h3>
                    <p className="text-gray-600 text-sm sm:text-base">Your budget goes where it truly matters. Our AI prioritizes top-performing units and cuts waste from poor performers.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                  <Icon icon="mdi:check" className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                  <h3 className="font-semibold text-lg sm:text-xl text-gray-900 mb-2">Adaptive Bidding & Budgeting</h3>
                    <p className="text-gray-600 text-sm sm:text-base">When the heat is on, we increase exposure; when it&apos;s not, we automatically control costs.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                  <Icon icon="mdi:check" className="w-5 h-5 text-orange-600" />
                  </div>
                  <div>
                  <h3 className="font-semibold text-lg sm:text-xl text-gray-900 mb-2">Save Time on Routine Work</h3>
                    <p className="text-gray-600 text-sm sm:text-base">Save 10+ hours a week. Skip the manual grind—DeepBI runs thousands of smart optimizations daily so you don&apos;t have to.</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                <div className="w-8 h-8 bg-cyan-100 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                  <Icon icon="mdi:check" className="w-5 h-5 text-cyan-600" />
                  </div>
                  <div>
                  <h3 className="font-semibold text-lg sm:text-xl text-gray-900 mb-2">Performance Gets Better Over Time</h3>
                    <p className="text-gray-600 text-sm sm:text-base">The more you use it, the smarter it gets. Our AI continuously learns and improves from your campaign data.</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                  <Icon icon="mdi:check" className="w-5 h-5 text-yellow-600" />
                  </div>
                  <div>
                  <h3 className="font-semibold text-lg sm:text-xl text-gray-900 mb-2">Fits All Product Types</h3>
                    <p className="text-gray-600 text-sm sm:text-base"> An adaptive system for every stage and every ASIN. From new launches to bestsellers, DeepBI customizes strategies for each.</p>
                </div>
              </div>
              
              <div className="mt-8 sm:mt-10 flex flex-col sm:flex-row gap-3 sm:gap-4">
                <button className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 sm:px-8 sm:py-4 rounded-xl text-base sm:text-lg font-bold hover:from-blue-700 hover:to-blue-800 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl">
                  Try for $0
                </button>
                <button className="w-full sm:w-auto border-2 border-gray-300 text-gray-700 px-6 py-3 sm:px-8 sm:py-4 rounded-xl text-base sm:text-lg font-bold hover:bg-gray-50 hover:border-gray-400 transform hover:scale-105 transition-all duration-300">
                  Book a Demo
                </button>
              </div>
            </div>
            
            <div className="relative" data-aos="fade-left">
              <Image 
                src="/images/Home/preview.webp" 
                alt="DeepBI Dashboard Features" 
                width={600}
                height={400}
                className="w-full h-auto object-contain rounded-lg shadow-lg" 
              />
            </div>
          </div>
        </div>
      </section>

      {/* What DeepBI Does for You */}
      <section id="what-deepbi-does" className="py-16 md:py-28 px-4 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-blue-100 rounded-full blur-3xl opacity-40"></div>
          <div className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-purple-100 rounded-full blur-3xl opacity-40"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10" data-aos="fade-up">
          <div className="text-center mb-12 lg:mb-16">
            <h2 className="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold mb-4 sm:mb-6 text-gray-900">
              What DeepBI <span className="text-blue-600">Does</span> for You
            </h2>
            <p className="text-xs sm:text-sm md:text-base lg:text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed px-4">
              Fully Automated – From Setup to Scalable Results
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
            <div className="space-y-6" data-aos="fade-right">
              {features.map((feature, index) => (
                <div
                  key={index}
                  className={`group relative flex items-start p-3 lg:p-4 rounded-2xl cursor-pointer transition-all duration-500 transform hover:-translate-y-1 border overflow-hidden
                    ${index === selectedFeatureIndex 
                      ? 'bg-gradient-to-br from-blue-50 to-blue-100 shadow-xl border-blue-200' 
                      : 'bg-white hover:bg-gray-50 shadow-md hover:shadow-lg border-gray-100'
                    }`}
                  onClick={() => setSelectedFeatureIndex(index)}
                >
                  {/* 背景装饰 */}
                  <div className={`absolute top-0 right-0 w-20 h-20 rounded-full -translate-y-10 translate-x-10 transition-all duration-500
                    ${index === selectedFeatureIndex ? 'bg-blue-400/20 scale-150' : 'bg-blue-400/10 group-hover:scale-125'}`}></div>
                  
                  <div className="relative z-10 flex items-start w-full">
                    <div className={`w-10 h-10 rounded-2xl flex items-center justify-center mr-4 flex-shrink-0 transition-all duration-300
                      ${index === selectedFeatureIndex 
                        ? 'bg-gradient-to-br from-blue-600 to-blue-700 text-white scale-110' 
                        : 'bg-gradient-to-br from-blue-100 to-blue-200 text-blue-600 group-hover:scale-110'
                      }`}
                    >
                      <Icon icon={feature.icon} className="w-5 h-5" />
                  </div>
                    <div className="flex-1">
                      <h3 className={`font-bold text-sm sm:text-base lg:text-lg mb-2 transition-colors duration-300
                        ${index === selectedFeatureIndex ? 'text-blue-900' : 'text-gray-900'}`}>
                        {feature.title}
                      </h3>
                      <p className="text-gray-600 text-xs sm:text-sm leading-relaxed">{feature.description}</p>
                    </div>
                  </div>
                </div>
              ))}

            </div>
            
            <div className="relative" data-aos="fade-left">
              <div className="relative bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl p-5 lg:p-8 h-[360px] lg:h-[460px] overflow-hidden">
                {/* 背景装饰 */}
                <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full -translate-y-20 translate-x-20"></div>
                <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-purple-400/20 to-blue-400/20 rounded-full translate-y-16 -translate-x-16"></div>
                
                <div className="relative z-10 h-full flex items-center justify-center">
                  <Image 
                src={features[selectedFeatureIndex].image}
                alt={features[selectedFeatureIndex].title} 
                    width={600}
                    height={500}
                    className="w-full h-auto max-h-full object-contain rounded-2xl shadow-2xl transform hover:scale-105 transition-all duration-500" 
              />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How DeepBI Works */}
      <section id="how-it-works" className="py-20 md:py-32 px-4 relative bg-white overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-100 rounded-full blur-3xl opacity-30"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-100 rounded-full blur-3xl opacity-30"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10" data-aos="fade-up">
          <div className="text-center mb-12 lg:mb-16">
            <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6 text-gray-900">
              How <span className="text-blue-600">DeepBI</span> Works
            </h2>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed px-4">
              5 AI Principles Behind Smarter Ad Optimization
            </p>
          </div>
          
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* 时间线 */}
              <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200"></div>
              
              <div className="space-y-12">
                <div className="relative flex items-start" data-aos="fade-up" data-aos-delay="100">
                  <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-xl relative z-10 mr-8 flex-shrink-0">
                    1
                </div>
                  <div className="pt-3">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">Broad Traffic Discovery</h3>
                    <p className="text-gray-600 text-xs sm:text-sm mb-2">✅ Uncover more potential with no extra effort.</p>
                    <p className="text-gray-600 text-xs sm:text-sm">Explore long-tail keywords + competitor ASINs to uncover scalable reach.</p>
                  </div>
            </div>
            
                <div className="relative flex items-start" data-aos="fade-up" data-aos-delay="200">
                  <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center text-white font-bold text-xl relative z-10 mr-8 flex-shrink-0">
                    2
                </div>
                  <div className="pt-3">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">Balance Exploration & Conversion</h3>
                    <p className="text-gray-600 text-xs sm:text-sm mb-2">✅ Drive growth without losing control.</p>
                    <p className="text-gray-600 text-xs sm:text-sm">Find the sweet spot between discovery and results.</p>
                  </div>
            </div>
            
                <div className="relative flex items-start" data-aos="fade-up" data-aos-delay="300">
                  <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl relative z-10 mr-8 flex-shrink-0">
                    3
                </div>
                  <div className="pt-3">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">Smart Budget Flow</h3>
                    <p className="text-gray-600 text-xs sm:text-sm mb-2">✅ Every dollar is spent where it matters most.</p>
                    <p className="text-gray-600 text-xs sm:text-sm">Allocate more budget to what works, cut what doesn&apos;t.</p>
                  </div>
            </div>
            
                <div className="relative flex items-start" data-aos="fade-up" data-aos-delay="400">
                  <div className="w-16 h-16 bg-orange-600 rounded-full flex items-center justify-center text-white font-bold text-xl relative z-10 mr-8 flex-shrink-0">
                    4
                </div>
                  <div className="pt-3">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">Tiered Bidding Strategy</h3>
                    <p className="text-gray-600 text-xs sm:text-sm mb-2">✅ Stop wasting money on underperformers.</p>
                    <p className="text-gray-600 text-xs sm:text-sm">Raise bids on top performers, reduce for underperformers.</p>
                  </div>
            </div>
            
                <div className="relative flex items-start" data-aos="fade-up" data-aos-delay="500">
                  <div className="w-16 h-16 bg-cyan-600 rounded-full flex items-center justify-center text-white font-bold text-xl relative z-10 mr-8 flex-shrink-0">
                    5
                </div>
                  <div className="pt-3">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">Traffic Reinforcement Matrix</h3>
                    <p className="text-gray-600 text-xs sm:text-sm mb-2">✅ Keep your traffic running — no interruptions.</p>
                    <p className="text-gray-600 text-xs sm:text-sm">Bridge gaps across AUTO, MANUAL, ASIN campaigns for seamless flow.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Real Results */}
      <section id="results" className="py-20 md:py-32 px-4 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-green-100 rounded-full blur-3xl opacity-40"></div>
          <div className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-blue-100 rounded-full blur-3xl opacity-40"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10" data-aos="fade-up">
          <div className="text-center mb-12 lg:mb-16">
            <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6 text-gray-900">
              Real <span className="text-green-600">Results</span> from Real Sellers
            </h2>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed px-4">
              The data speaks for itself, thanks to our AI
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="text-center bg-white p-8 rounded-lg shadow-md" data-aos="fade-right">
              <div className="mb-6">
                <Image
                  src="/images/svg/acos-chart.svg"
                  alt="ACOS Decrease Chart"
                  width={300}
                  height={200}
                  className="mx-auto"
                />
              </div>
              <div className="text-4xl font-bold text-red-600 mb-2">-32%</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">ACOS Decrease</h3>
              <p className="text-gray-600">Average reduction across all seller categories</p>
              <i className="text-gray-500 text-sm">Results may vary based on product and market conditions.</i>
            </div>
            
            <div className="text-center bg-white p-8 rounded-lg shadow-md" data-aos="fade-left">
              <div className="mb-6">
                <Image
                  src="/images/svg/sales-growth-chart.svg"
                  alt="Sales Growth Chart"
                  width={300}
                  height={200}
                  className="mx-auto"
                />
              </div>
              <div className="text-4xl font-bold text-green-600 mb-2">+47%</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Sales Growth</h3>
              <p className="text-gray-600">Average increase in monthly sales after 90 days</p>
              <i className="text-gray-500 text-sm">Results may vary based on product and market conditions.</i>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row justify-center gap-4 lg:gap-6 mt-16" data-aos="fade-up" data-aos-delay="400">
            <button className="group bg-white border-2 border-gray-300 px-8 py-4 rounded-xl font-bold text-lg hover:bg-blue-600 hover:border-blue-600 hover:text-white transition-all duration-300 flex items-center justify-center transform hover:scale-105">
              <span>See Case Studies</span>
              <Icon icon="mdi:arrow-right" className="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
            </button>
          </div>
        </div>
      </section>

      {/* Trust Section */}
      <section id="trust" className="py-20 md:py-32 px-4 bg-white relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 z-0 left-1/4">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-green-100 rounded-full blur-3xl opacity-30"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-blue-100 rounded-full blur-3xl opacity-30"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10" data-aos="fade-up">
          <div className="text-center mb-12 lg:mb-16">
            <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6 text-gray-900">
              Why Hundreds of Amazon Sellers <span className="text-blue-600">Trust</span> DeepBI
            </h2>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed px-4">
              Built with compliance, stability, and global readiness in mind
            </p>
          </div>
          
          <div className="flex flex-col lg:flex-row justify-center items-center mb-20 gap-8 lg:gap-16" data-aos="fade-up" data-aos-delay="200">
            <div className="group relative bg-white p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100">
              <div className="relative h-[120px] w-[250px]">
              <Image 
                src="/images/Home/amazonADS-web.png" 
                alt="Amazon Ads" 
                fill
                style={{ objectFit: 'contain' }}
                  className="group-hover:scale-105 transition-transform duration-300"
              />
            </div>
            </div>
            
            <div className="hidden lg:block w-px h-16 bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>
            
            <div className="group relative bg-white p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100">
              <div className="relative h-[120px] w-[250px]">
                <Image 
                src="/images/Home/amazonSPN-web.png" 
                alt="SPN Partner" 
                  fill
                  style={{ objectFit: 'contain' }}
                  className="group-hover:scale-105 transition-transform duration-300"
                />
              </div>
            </div>

            <div className="hidden lg:block w-px h-16 bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>
            
            <div className="group relative bg-white p-8 rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100">
              <div className="relative h-[120px] w-[250px]">
              <Image 
                src="/images/Home/amazonSTORE-web.png" 
                alt="Amazon Store" 
                fill
                style={{ objectFit: 'contain' }}
                  className="group-hover:scale-105 transition-transform duration-300"
              />
            </div>
            </div>
              </div>
              
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-6" data-aos="fade-up" data-aos-delay="100">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon icon="mdi:shield-check" className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="font-semibold text-lg text-gray-900 mb-2">Amazon API Compliant</h3>
              <p className="text-gray-600 text-sm">Fully compliant with Amazon&apos;s API rules and regulations to ensure account security.</p>
              </div>
              
            <div className="text-center p-6" data-aos="fade-up" data-aos-delay="200">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon icon="mdi:globe" className="w-8 h-8 text-blue-600" />
                </div>
              <h3 className="font-semibold text-lg text-gray-900 mb-2">Global Capability</h3>
              <p className="text-gray-600 text-sm">Supporting all major Amazon marketplaces with region-specific optimization strategies.</p>
            </div>
            
            <div className="text-center p-6" data-aos="fade-up" data-aos-delay="300">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon icon="mdi:headset" className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="font-semibold text-lg text-gray-900 mb-2">Dedicated Support</h3>
              <p className="text-gray-600 text-sm">Expert customer success team to help with strategy questions and technical assistance.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="py-20 md:py-32 px-4 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-yellow-100 rounded-full blur-3xl opacity-40"></div>
          <div className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-blue-100 rounded-full blur-3xl opacity-40"></div>
        </div>
        
        <div className="max-w-7xl mx-auto relative z-10" data-aos="fade-up">
          <div className="text-center mb-12 lg:mb-16">
            <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6 text-gray-900">
              What Real <span className="text-yellow-600">Sellers</span> Say
            </h2>
            <p className="text-sm sm:text-base md:text-lg lg:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed px-4">
              Lower ACOS. Higher sales. Less stress.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg border border-gray-200" data-aos="fade-up" data-aos-delay="100">
              <div className="flex mb-4">
                {[...Array(5)].map((_, i) => (
                  <Icon key={i} icon="mdi:star" className="w-4 h-4 text-yellow-400" />
                ))}
              </div>
              <p className="text-gray-600 mb-4 italic">&quot;We used to adjust ads manually every day. It was exhausting. After switching to DeepBI, ACoS dropped from 38% to 21% — and it stayed stable.&quot;</p>
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                  <Image 
                    src="/images/Home/avatar/avatar1.jpg" 
                    alt="Consumer Electronics Seller" 
                    className="w-full h-full object-cover"
                    width={40}
                    height={40}
                  />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Consumer Electronics Seller</p>
                  <p className="text-sm text-gray-500">Austin, US</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg border border-gray-200" data-aos="fade-up" data-aos-delay="200">
              <div className="flex mb-4">
                {[...Array(5)].map((_, i) => (
                  <Icon key={i} icon="mdi:star" className="w-4 h-4 text-yellow-400" />
                ))}
              </div>
              <p className="text-gray-600 mb-4 italic">&quot;Managing multiple EU sites used to be a mess. DeepBI&apos;s unified strategy helped us save time and reduced manual effort by over 80%.&quot;</p>
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                  <Image 
                    src="/images/Home/avatar/avatar2.jpg" 
                    alt="Apparel Operations Manager" 
                    className="w-full h-full object-cover"
                    width={40}
                    height={40}
                  />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Apparel Operations</p>
                  <p className="text-sm text-gray-500">Manager, Berlin, DE</p>
                </div>
              </div>
            </div>
            
            <div className="bg-white p-6 rounded-lg border border-gray-200" data-aos="fade-up" data-aos-delay="300">
              <div className="flex mb-4">
                {[...Array(5)].map((_, i) => (
                  <Icon key={i} icon="mdi:star" className="w-4 h-4 text-yellow-400" />
                ))}
              </div>
              <p className="text-gray-600 mb-4 italic">&quot;I don&apos;t have a big team. DeepBI runs thousands of optimizations daily, so I can focus on inventory and service.&quot;</p>
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                  <Image 
                    src="/images/Home/avatar/avatar3.jpg" 
                    alt="Solo Amazon Seller" 
                    className="w-full h-full object-cover"
                    width={40}
                    height={40}
                  />
                </div>
                <div>
                  <p className="font-semibold text-gray-900">Solo Amazon Seller</p>
                  <p className="text-sm text-gray-500">Toronto, CA</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section id="try-it-now" className="py-20 px-4 relative overflow-hidden bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800">
        {/* 背景装饰 */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-white/5 rounded-full blur-3xl"></div>
        </div>
        
        <div className="max-w-4xl mx-auto text-center relative z-10" data-aos="fade-up">
          <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6 text-white">
            Ready to optimize your <span className="text-blue-200">Amazon ads?</span>
          </h2>
          
          {/* Email Subscription Component */}
          <EmailSubscription />
          
          {/* Commented out for future use when officially launched */}
          {/* <p className="text-sm sm:text-base md:text-lg lg:text-xl text-blue-100 mb-8 sm:mb-12 max-w-2xl mx-auto leading-relaxed px-4">
            Just $0 to start. No setup required. Cancel anytime.
          </p>
          
          <div className="flex flex-col md:flex-row justify-center gap-4 md:gap-6">
            <button className="group relative px-10 py-5 bg-white text-blue-600 rounded-2xl text-xl font-bold hover:bg-blue-50 transform hover:scale-105 transition-all duration-300 shadow-2xl overflow-hidden">
              <span className="relative z-10">Try for $0</span>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
            <button className="px-10 py-5 border-2 border-white/30 text-white rounded-2xl text-xl font-bold hover:bg-white/10 hover:border-white/50 transform hover:scale-105 transition-all duration-300 backdrop-blur-sm">
              Book a Demo
            </button>
          </div> */}
          
          {/* <div className="flex flex-col sm:flex-row justify-center items-center gap-6">
            <div className="flex items-center bg-white/10 backdrop-blur-sm py-3 px-6 rounded-full border border-white/20">
              <Icon icon="mdi:shield-check" className="w-6 h-6 text-green-400 mr-3" />
              <span className="text-white font-medium">Official Amazon Partner</span>
            </div>
            
            <div className="flex items-center bg-white/10 backdrop-blur-sm py-3 px-6 rounded-full border border-white/20">
              <Icon icon="mdi:security" className="w-6 h-6 text-blue-200 mr-3" />
              <span className="text-white font-medium">100% Secure</span>
            </div>
          </div> */}
        </div>
      </section>
    </main>
  );
};

export default HomePage;