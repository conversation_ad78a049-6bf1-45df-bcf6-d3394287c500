<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="chart-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stop-color="#3b82f6" />
          <stop offset="100%" stop-color="#93c5fd" />
        </linearGradient>
        <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="#3b82f6" flood-opacity="0.2"/>
        </filter>
      </defs>
      
      <!-- Background Grid -->
      <g stroke="#e0e7ff" stroke-width="0.5" opacity="0.5">
        <line x1="50" y1="50" x2="550" y2="50" /><line x1="50" y1="80" x2="550" y2="80" /><line x1="50" y1="110" x2="550" y2="110" /><line x1="50" y1="140" x2="550" y2="140" /><line x1="50" y1="170" x2="550" y2="170" /><line x1="50" y1="200" x2="550" y2="200" /><line x1="50" y1="230" x2="550" y2="230" /><line x1="50" y1="260" x2="550" y2="260" /><line x1="50" y1="290" x2="550" y2="290" /><line x1="50" y1="320" x2="550" y2="320" />
        <line x1="50" y1="50" x2="50" y2="350" /><line x1="85" y1="50" x2="85" y2="350" /><line x1="120" y1="50" x2="120" y2="350" /><line x1="155" y1="50" x2="155" y2="350" /><line x1="190" y1="50" x2="190" y2="350" /><line x1="225" y1="50" x2="225" y2="350" /><line x1="260" y1="50" x2="260" y2="350" /><line x1="295" y1="50" x2="295" y2="350" /><line x1="330" y1="50" x2="330" y2="350" /><line x1="365" y1="50" x2="365" y2="350" /><line x1="400" y1="50" x2="400" y2="350" /><line x1="435" y1="50" x2="435" y2="350" /><line x1="470" y1="50" x2="470" y2="350" /><line x1="505" y1="50" x2="505" y2="350" /><line x1="540" y1="50" x2="540" y2="350" />
      </g>

      <!-- Main Chart Area -->
      <rect x="50" y="50" width="500" height="300" fill="#ffffff" rx="10" ry="10" filter="url(#shadow)"/>
      
      <!-- Graph Lines (representing optimization) -->
      <polyline points="70,300 120,250 170,280 220,230 270,260 320,210 370,240 420,190 470,220 520,170" 
                 fill="none" stroke="url(#chart-gradient)" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
      
      <!-- Glowing points -->
      <circle cx="70" cy="300" r="6" fill="#3b82f6" filter="url(#shadow)"/>
      <circle cx="220" cy="230" r="6" fill="#3b82f6" filter="url(#shadow)"/>
      <circle cx="370" cy="240" r="6" fill="#3b82f6" filter="url(#shadow)"/>
      <circle cx="520" cy="170" r="6" fill="#3b82f6" filter="url(#shadow)"/>

      <!-- AI Brain Icon -->
      <g transform="translate(480, 50)" opacity="0.8">
        <path d="M50 0 C77.6142 0 100 22.3858 100 50 C100 77.6142 77.6142 100 50 100 C22.3858 100 0 77.6142 0 50 C0 22.3858 22.3858 0 50 0 Z" fill="#3b82f6" />
        <path d="M50 20 C66.5685 20 80 33.4315 80 50 C80 66.5685 66.5685 80 50 80 C33.4315 80 20 66.5685 20 50 C20 33.4315 33.4315 20 50 20 Z" fill="#60a5fa" />
        <circle cx="35" cy="40" r="5" fill="#e0e7ff" />
        <circle cx="65" cy="40" r="5" fill="#e0e7ff" />
        <line x1="50" y1="55" x2="50" y2="70" stroke="#e0e7ff" stroke-width="2" stroke-linecap="round" />
        <path d="M40 70 Q50 75 60 70" stroke="#e0e7ff" stroke-width="2" fill="none" stroke-linecap="round" />
        <circle cx="50" cy="50" r="30" stroke="#e0e7ff" stroke-width="2" fill="none" stroke-dasharray="2 2" />
        <text x="50" y="58" text-anchor="middle" font-family="Arial" font-size="12" fill="#e0e7ff">AI</text>
      </g>
    </svg>