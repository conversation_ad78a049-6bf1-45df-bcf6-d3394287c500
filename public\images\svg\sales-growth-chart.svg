<svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
      <style>
        .title { font: bold 24px Arial; fill: #1e293b; }
        .label { font: 12px Arial; fill: #64748b; }
        .axis { stroke: #94a3b8; stroke-width: 1; }
        .bar { fill: #3b82f6; }
        .line { stroke: #ef4444; stroke-width: 3; fill: none; }
        .point { fill: #ffffff; stroke: #ef4444; stroke-width: 2; }
        .annotation { font: bold 14px Arial; fill: #ef4444; }
      </style>
      
      <rect x="0" y="0" width="800" height="400" fill="#ffffff"/>
      
      <text x="50" y="40" class="title">Monthly Sales Growth</text>
      
      <!-- 轴线 -->
      <line x1="100" y1="320" x2="700" y2="320" class="axis"/>
      <line x1="100" y1="100" x2="100" y2="320" class="axis"/>
      
      <!-- 柱状图 -->
      <rect x="115" y="270" width="30" height="50" class="bar" opacity="0.7"/>
      <rect x="175" y="265" width="30" height="55" class="bar" opacity="0.7"/>
      <rect x="235" y="260" width="30" height="60" class="bar" opacity="0.7"/>
      <rect x="295" y="240" width="30" height="80" class="bar" opacity="0.7"/>
      <rect x="355" y="220" width="30" height="100" class="bar" opacity="0.7"/>
      <rect x="415" y="200" width="30" height="120" class="bar" opacity="0.7"/>
      <rect x="475" y="180" width="30" height="140" class="bar" opacity="0.7"/>
      <rect x="535" y="160" width="30" height="160" class="bar" opacity="0.7"/>
      <rect x="595" y="140" width="30" height="180" class="bar" opacity="0.7"/>
      <rect x="655" y="120" width="30" height="200" class="bar" opacity="0.7"/>
      
      <!-- 趋势线 -->
      <polyline points="130,270 190,265 250,260 310,240 370,220 430,200 490,180 550,160 610,140 670,120" class="line"/>
      
      <!-- 数据点 -->
      <circle cx="130" cy="270" r="5" class="point"/>
      <circle cx="190" cy="265" r="5" class="point"/>
      <circle cx="250" cy="260" r="5" class="point"/>
      <circle cx="310" cy="240" r="5" class="point"/>
      <circle cx="370" cy="220" r="5" class="point"/>
      <circle cx="430" cy="200" r="5" class="point"/>
      <circle cx="490" cy="180" r="5" class="point"/>
      <circle cx="550" cy="160" r="5" class="point"/>
      <circle cx="610" cy="140" r="5" class="point"/>
      <circle cx="670" cy="120" r="5" class="point"/>
      
      <!-- X轴标签 -->
      <text x="130" y="340" class="label" text-anchor="middle">Jan</text>
      <text x="190" y="340" class="label" text-anchor="middle">Feb</text>
      <text x="250" y="340" class="label" text-anchor="middle">Mar</text>
      <text x="310" y="340" class="label" text-anchor="middle">Apr</text>
      <text x="370" y="340" class="label" text-anchor="middle">May</text>
      <text x="430" y="340" class="label" text-anchor="middle">Jun</text>
      <text x="490" y="340" class="label" text-anchor="middle">Jul</text>
      <text x="550" y="340" class="label" text-anchor="middle">Aug</text>
      <text x="610" y="340" class="label" text-anchor="middle">Sep</text>
      <text x="670" y="340" class="label" text-anchor="middle">Oct</text>
      
      <!-- Y轴标签 -->
      <text x="80" y="320" class="label" text-anchor="end">$0K</text>
      <text x="80" y="270" class="label" text-anchor="end">$20K</text>
      <text x="80" y="220" class="label" text-anchor="end">$40K</text>
      <text x="80" y="170" class="label" text-anchor="end">$60K</text>
      <text x="80" y="120" class="label" text-anchor="end">$80K</text>
      
      <!-- 优化标记线 -->
      <line x1="250" y1="100" x2="250" y2="320" stroke="#94a3b8" stroke-width="1" stroke-dasharray="5,5"/>
      <text x="255" y="115" class="label">DeepBI Optimization Started</text>
      
      <!-- 增长注解 -->
      <text x="550" y="90" class="annotation">+47% Sales Growth</text>
      <line x1="610" y1="95" x2="610" y2="130" stroke="#ef4444" stroke-width="1" stroke-dasharray="2,2"/>
    </svg>