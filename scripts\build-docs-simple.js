const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('======== 构建文档站 (my-app) ========');

const myAppDir = path.join(__dirname, '..', 'my-app');
const myAppOutDir = path.join(myAppDir, 'out');

// 检查 my-app 目录是否存在
if (!fs.existsSync(myAppDir)) {
  console.error('错误: my-app 目录不存在');
  process.exit(1);
}

try {
  console.log('1) 在 my-app 中执行构建...');
  execSync('npm run build', { cwd: myAppDir, stdio: 'inherit' });
} catch (e) {
  console.error('my-app 构建失败:', e.message);
  process.exit(1);
}

// 检查构建输出
if (!fs.existsSync(myAppOutDir)) {
  console.error('错误: my-app/out 目录不存在，构建可能失败');
  process.exit(1);
}

console.log('2) 文档站构建完成');
console.log('   文档站构建目录: my-app/out/');
console.log('');
console.log('📁 部署目录结构:');
console.log('   主站: out/ → nginx 根目录 (/)');
console.log('   文档: my-app/out/ → nginx /docs 路径');
console.log('');
console.log('🚀 使用测试服务器预览:');
console.log('   node test-server.js');
console.log('   然后访问 http://localhost:8080');
