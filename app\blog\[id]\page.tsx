'use client';

import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import blogData from '@/components/Blog/blogData';
import { useEffect } from 'react';
import { trackBlogView, trackBlogClick } from '@/utils/analytics';

interface BlogDetailProps {
  params: {
    id: string;
  };
}

// 生成静态参数
export async function generateStaticParams() {
  return blogData.map((post) => ({
    id: post.id.toString(),
  }));
}



export default function BlogDetail({ params }: BlogDetailProps) {
  const blog = blogData.find(blog => blog.id === parseInt(params.id));

  if (!blog) {
    notFound();
  }

  // 页面加载时追踪博客浏览事件
  useEffect(() => {
    if (blog) {
      trackBlogView(blog.id, blog.title);
    }
  }, [blog]);

  // 处理相关文章点击事件
  const handleRelatedBlogClick = (blogId: number, blogTitle: string) => {
    trackBlogClick(blogId, blogTitle, 'related_posts');
  };

  // 获取文章内容
  const articleContent = blog.content?.content || blog.paragraph;
  const excerpt = blog.content?.excerpt || blog.paragraph;
  const readingTime = blog.content?.readingTime || 5; // 默认5分钟
  const sections = blog.content?.sections || [];

  // 获取相关文章
  const relatedPostIds = blog.content?.relatedPosts || [];
  const relatedPosts = relatedPostIds.length > 0 
    ? blogData.filter(item => relatedPostIds.includes(item.id))
    : blogData.filter(item => item.id !== blog.id).slice(0, 3);

  return (
    <>
      {/* Header with Back Navigation */}
      <section className="pt-32 pb-8">
        <div className="container mx-auto px-4">
          <Link 
            href="/blog" 
            className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium mb-8 group"
          >
            <svg className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Blog
          </Link>
        </div>
      </section>

      {/* Article Header */}
      <section className="pb-8">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-6">
              {blog.tags.map((tag, index) => (
                <span
                  key={index}
                  className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-sm font-medium"
                >
                  {tag}
                </span>
              ))}
            </div>
            
            {/* Title */}
            <h1 className="text-3xl font-bold mb-6" style={{ lineHeight: '1.5' }}>
              {blog.title}
            </h1>
            
            {/* Meta Info */}
            <div className="flex items-center space-x-6 mb-2 text-gray-600">
              <div className="flex items-center space-x-3">
                <div className="relative w-12 h-12 rounded-full overflow-hidden">
                  <Image
                    src={blog.author.image}
                    alt={blog.author.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <p className="font-medium text-gray-900">{blog.author.name}</p>
                  <p className="text-sm text-gray-600">{blog.author.designation}</p>
                </div>
              </div>
              <div className="flex items-center space-x-4 text-sm">
                <span>{blog.publishDate}</span>
                <span>•</span>
                <span>{readingTime} min read</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Image */}
      <section className="pb-12">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <div className="aspect-[16/9] relative rounded-2xl overflow-hidden">
              <Image
                src={blog.image}
                alt={blog.title}
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Article Content with Sidebar */}
      <section className="pb-16 relative">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <div className="prose prose-lg prose-gray max-w-none">
              {/* 文章摘要 */}
              <div className="bg-blue-50 p-6 rounded-xl mb-8">
                <p className="text-lg text-gray-700 leading-relaxed mb-0">
                  {excerpt}
                </p>
              </div>
              
              {/* 文章主体内容 */}
              <div 
                className="space-y-6 text-gray-700 leading-relaxed"
                dangerouslySetInnerHTML={{ __html: articleContent }}
              />
            </div>
          </div>
        </div>

        {/* 目录侧边栏 - 绝对定位到右侧 */}
        {sections.length > 0 && (
          <div className="hidden 2xl:block absolute top-0 right-14 w-80">
            <div className="sticky top-12">
              <div className="bg-gray-50 p-6 rounded-xl">
                <h2 className="text-lg font-bold text-gray-900 mb-4">On this page</h2>
                <nav>
                  <ul className="space-y-2">
                    {sections.map((section) => (
                      <li key={section.id}>
                        <a
                          href={`#${section.id}`}
                          className="text-#1E51DB hover:text-blue-800 text-sm block py-1"
                          style={{ paddingLeft: `${(section.level - 2) * 12}px` }}
                        >
                          {section.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        )}
      </section>

      {/* Related Articles */}
      {relatedPosts.length > 0 && (
        <section className="py-16 bg-slate-50">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">
                Related Articles
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {relatedPosts.map((relatedBlog) => (
                  <article key={relatedBlog.id} className="group">
                    <Link
                      href={`/blog/${relatedBlog.id}`}
                      className="block"
                      onClick={() => handleRelatedBlogClick(relatedBlog.id, relatedBlog.title)}
                    >
                      <div className="bg-white rounded-xl overflow-hidden hover:shadow-xl transition-all duration-300">
                        <div className="aspect-[16/10] relative">
                          <Image
                            src={relatedBlog.image}
                            alt={relatedBlog.title}
                            fill
                            className="group-hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                        <div className="p-6">
                          <span className="text-blue-600 text-sm font-medium">
                            {relatedBlog.tags[0]}
                          </span>
                          <h3 className="text-lg font-bold text-gray-900 mt-2 mb-3 group-hover:text-blue-600 transition-colors line-clamp-2">
                            {relatedBlog.title}
                          </h3>
                          <p className="text-gray-600 text-sm line-clamp-3">
                            {relatedBlog.content?.excerpt || relatedBlog.paragraph}
                          </p>
                        </div>
                      </div>
                    </Link>
                  </article>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}
    </>
  );
}