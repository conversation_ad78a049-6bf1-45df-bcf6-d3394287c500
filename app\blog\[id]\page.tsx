import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import blogData from '@/components/Blog/blogData';
import { Metadata } from 'next';
import BlogClickTracker from '@/components/BlogClickTracker';
import BlogViewTracker from '@/components/BlogViewTracker';

interface BlogDetailProps {
  params: {
    id: string;
  };
}

// 生成静态参数
export async function generateStaticParams() {
  return blogData.map((post) => ({
    id: post.id.toString(),
  }));
}

// 生成元数据
export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const post = blogData.find((post) => post.id.toString() === params.id);

  if (!post) {
    return {
      title: 'Blog Not Found | DeepBI',
      description: '',
    };
  }

  // 使用自定义元数据或默认值
  const metaTitle = post.content?.metaTitle || post.title;
  const metaDescription = post.content?.metaDescription || post.content?.excerpt || post.paragraph;

  return {
    title: `${metaTitle} - DeepBI Blog`,
    description: metaDescription,
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      images: [post.image],
      type: 'article',
    },
  };
}



export default function BlogDetail({ params }: BlogDetailProps) {
  const blog = blogData.find(blog => blog.id === parseInt(params.id));

  if (!blog) {
    notFound();
  }



  // 获取文章内容
  const articleContent = blog.content?.content || blog.paragraph;
  const excerpt = blog.content?.excerpt || blog.paragraph;
  const readingTime = blog.content?.readingTime || 5; // 默认5分钟
  const sections = blog.content?.sections || [];

  // 获取相关文章
  const relatedPostIds = blog.content?.relatedPosts || [];
  const relatedPosts = relatedPostIds.length > 0 
    ? blogData.filter(item => relatedPostIds.includes(item.id))
    : blogData.filter(item => item.id !== blog.id).slice(0, 3);

  return (
    <>
      {/* 添加页面浏览追踪 */}
      <BlogViewTracker blogId={blog.id} blogTitle={blog.title} />

      {/* Header with Back Navigation */}
      <section className="pt-16 md:pt-20 lg:pt-32 pb-4 md:pb-8">
        <div className="container mx-auto px-4">
          <Link
            href="/blog"
            className="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium mb-4 md:mb-6 lg:mb-8 group text-sm md:text-base"
          >
            <svg className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Back to Blog
          </Link>
        </div>
      </section>

      {/* Article Header */}
      <section className="pb-4 md:pb-6 lg:pb-8">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-3 md:mb-4 lg:mb-6">
              {blog.tags.map((tag, index) => (
                <span
                  key={index}
                  className="bg-blue-100 text-blue-600 px-2 md:px-3 py-1 rounded-full text-xs md:text-sm font-medium"
                >
                  {tag}
                </span>
              ))}
            </div>

            {/* Title */}
            <h1 className="text-lg md:text-xl lg:text-2xl xl:text-3xl font-bold mb-3 md:mb-4 lg:mb-6 leading-tight">
              {blog.title}
            </h1>
            
            {/* Meta Info */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-3 sm:space-y-0 mb-4 md:mb-6 text-gray-600">
              <div className="flex items-center space-x-3">
                <div className="relative w-10 md:w-12 h-10 md:h-12 rounded-full overflow-hidden">
                  <Image
                    src={blog.author.image}
                    alt={blog.author.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <p className="font-medium text-gray-900 text-sm md:text-base">{blog.author.name}</p>
                  <p className="text-xs md:text-sm text-gray-600">{blog.author.designation}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 md:space-x-4 text-xs md:text-sm">
                <span>{blog.publishDate}</span>
                <span>•</span>
                <span>{readingTime} min read</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Image */}
      <section className="pb-8 md:pb-12">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <div className="aspect-[16/9] relative rounded-xl md:rounded-2xl overflow-hidden">
              <Image
                src={blog.image}
                alt={blog.title}
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Article Content with Sidebar */}
      <section className="pb-10 md:pb-12 lg:pb-16 relative">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <div className="prose prose-sm md:prose-lg prose-gray max-w-none">
              {/* 文章摘要 */}
              <div className="bg-blue-50 p-3 md:p-4 lg:p-6 rounded-xl mb-5 md:mb-6 lg:mb-8">
                <p className="text-sm md:text-base lg:text-lg text-gray-700 leading-relaxed mb-0">
                  {excerpt}
                </p>
              </div>

              {/* 文章主体内容 */}
              <div
                className="space-y-3 md:space-y-4 lg:space-y-6 text-gray-700 leading-relaxed text-sm md:text-base"
                dangerouslySetInnerHTML={{ __html: articleContent }}
              />
            </div>
          </div>
        </div>

        {/* 目录侧边栏 - 绝对定位到右侧 */}
        {sections.length > 0 && (
          <div className="hidden 2xl:block absolute top-0 right-14 w-80">
            <div className="sticky top-12">
              <div className="bg-gray-50 p-6 rounded-xl">
                <h2 className="text-lg font-bold text-gray-900 mb-4">On this page</h2>
                <nav>
                  <ul className="space-y-2">
                    {sections.map((section) => (
                      <li key={section.id}>
                        <a
                          href={`#${section.id}`}
                          className="text-#1E51DB hover:text-blue-800 text-sm block py-1"
                          style={{ paddingLeft: `${(section.level - 2) * 12}px` }}
                        >
                          {section.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        )}
      </section>

      {/* Related Articles */}
      {relatedPosts.length > 0 && (
        <section className="py-10 md:py-12 lg:py-16 bg-slate-50">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-xl md:text-2xl lg:text-3xl font-bold text-gray-900 mb-6 md:mb-8 lg:mb-12 text-center">
                Related Articles
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 md:gap-6 lg:gap-8">
                {relatedPosts.map((relatedBlog) => (
                  <article key={relatedBlog.id} className="group">
                    <BlogClickTracker
                      blogId={relatedBlog.id}
                      blogTitle={relatedBlog.title}
                      href={`/blog/${relatedBlog.id}`}
                      source="related_posts"
                      className="block"
                    >
                      <div className="bg-white rounded-xl overflow-hidden hover:shadow-xl transition-all duration-300">
                        <div className="aspect-[16/10] relative">
                          <Image
                            src={relatedBlog.image}
                            alt={relatedBlog.title}
                            fill
                            className="group-hover:scale-105 transition-transform duration-300"
                          />
                        </div>
                        <div className="p-4 md:p-6">
                          {/* <span className="text-blue-600 text-xs md:text-sm font-medium">
                            {relatedBlog.tags[0]}
                          </span> */}
                          <h3 className="text-base md:text-lg font-bold text-gray-900 mt-2 mb-3 group-hover:text-blue-600 transition-colors line-clamp-2">
                            {relatedBlog.title}
                          </h3>
                          <p className="text-gray-600 text-xs md:text-sm line-clamp-3">
                            {relatedBlog.content?.excerpt || relatedBlog.paragraph}
                          </p>
                        </div>
                      </div>
                    </BlogClickTracker>
                  </article>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}
    </>
  );
}