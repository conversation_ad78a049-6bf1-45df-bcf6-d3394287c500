const fs = require('fs');
const path = require('path');

console.log('======== 博客内容格式验证 ========');

try {
  // 读取博客数据
  const blogDataPath = path.join(__dirname, '..', 'components', 'Blog', 'blogData.ts');
  console.log('1. 读取博客数据文件...');
  
  if (!fs.existsSync(blogDataPath)) {
    console.error('   ❌ 博客数据文件不存在');
    process.exit(1);
  }
  
  const blogDataContent = fs.readFileSync(blogDataPath, 'utf-8');
  console.log('   ✓ 博客数据文件读取成功');
  
  // 简单的数据验证
  console.log('2. 验证博客内容格式...');
  
  let totalPosts = 0;
  let issues = [];
  
  // 计算博客对象数量
  const idMatches = blogDataContent.match(/id:\s*\d+/g);
  if (idMatches) {
    totalPosts = idMatches.length;
    console.log(`   找到 ${totalPosts} 篇博客文章`);
  }
  
  // 检查基本结构
  const requiredFields = ['title', 'paragraph', 'image', 'author', 'tags', 'publishDate'];
  const authorFields = ['name', 'image', 'designation'];
  
  // 检查基本字段是否存在
  requiredFields.forEach(field => {
    if (!blogDataContent.includes(`${field}:`)) {
      issues.push(`缺少必需字段: ${field}`);
    }
  });
  
  // 检查作者字段
  authorFields.forEach(field => {
    if (!blogDataContent.includes(`${field}:`)) {
      issues.push(`作者信息缺少字段: ${field}`);
    }
  });
  
  // 检查是否有使用新内容格式的文章
  const hasNewFormat = blogDataContent.includes('content: {');
  const hasExcerpt = blogDataContent.includes('excerpt:');
  const hasReadingTime = blogDataContent.includes('readingTime:');
  
  console.log('3. 内容格式分析...');
  
  if (hasNewFormat) {
    console.log('   ✓ 发现使用新内容格式的文章');
    
    if (!hasExcerpt) {
      issues.push('建议为所有文章添加excerpt字段');
    }
    
    if (!hasReadingTime) {
      issues.push('建议为所有文章添加readingTime字段');
    }
  } else {
    console.log('   ⚠️  建议为所有文章添加content字段');
  }
  
  // 检查标签格式
  const tagMatches = blogDataContent.match(/tags:\s*\[([^\]]+)\]/g);
  if (tagMatches) {
    console.log(`   ✓ 找到 ${tagMatches.length} 个标签定义`);
    
    tagMatches.forEach((match, index) => {
      const tags = match.match(/\[([^\]]+)\]/)[1].split(',').map(tag => tag.trim().replace(/['"]/g, ''));
      if (tags.length < 3 || tags.length > 5) {
        issues.push(`文章${index + 1}标签数量应为3-5个，当前有${tags.length}个`);
      }
    });
  }
  
  console.log('4. 验证结果总结...');
  console.log(`   总文章数: ${totalPosts}`);
  console.log(`   发现问题: ${issues.length}`);
  
  if (issues.length > 0) {
    console.log('\n5. 发现的问题:');
    issues.forEach(issue => {
      console.log(`   - ${issue}`);
    });
  }
  
  console.log('\n6. 建议改进:');
  console.log('   - 确保所有文章都包含完整的content字段');
  console.log('   - 添加readingTime估算');
  console.log('   - 使用excerpt字段提供文章摘要');
  console.log('   - 确保标签数量在3-5个之间');
  console.log('   - 检查图片路径是否正确');
  
  console.log('\n======== 验证完成 ========');
  
  if (issues.length === 0) {
    console.log('✅ 所有文章格式都正确！');
  } else {
    console.log('⚠️  发现一些格式问题，建议修正');
  }
  
} catch (error) {
  console.error('内容验证出错:', error);
  process.exit(1);
}
