'use client'

import { useEffect } from 'react'
import { chatwootConfig } from '@/config/chatwoot'

declare global {
  interface Window {
    chatwootSettings: {
      position: 'right' | 'left'
      type: 'expanded_bubble' | 'standard'
    }
    chatwootSDK: {
      run: (config: {
        websiteToken: string
        baseUrl: string
      }) => void
    }
    $chatwoot: {
      setUser: (identifier: string, userProperties: {
        email: string
        name?: string
      }) => void
      setCustomAttributes: (attributes: Record<string, any>) => void
      setLabel: (label: string) => void
    }
  }
}

const ChatwootWidget = () => {
  useEffect(() => {
    if (window.$chatwoot) { 
      window.$chatwoot.reset();
    }

    // 如果 Chatwoot 被禁用，则不加载
    if (!chatwootConfig.enabled) {
      return
    }

    // 设置 Chatwoot 配置
    window.chatwootSettings = { 
      position: chatwootConfig.position, 
      type: chatwootConfig.type 
    }

    // 创建并加载 Chatwoot 脚本
    const loadChatwoot = () => {
      const script = document.createElement('script')
      script.src = chatwootConfig.baseUrl + '/packs/js/sdk.js'
      script.async = true
      
      script.onload = function () {
        if (window.chatwootSDK) {
          // 初始化 Chatwoot
          window.chatwootSDK.run({
            websiteToken: chatwootConfig.websiteToken,
            baseUrl: chatwootConfig.baseUrl
          })
        }
      }
      
      document.head.appendChild(script)
    }

    // 等待Chatwoot完全加载完成后再设置用户
    window.addEventListener('chatwoot:ready', function () {
      console.log('Chatwoot is ready, setting user...');
      window.$chatwoot.setLabel("website");

      // 设置自定义属性
      window.$chatwoot.setCustomAttributes({
        en_website:'英文官网'
      });
    });

    // 监听消息事件，确保在对话开始前设置标签
    window.addEventListener('chatwoot:on-message', function(e: any) {
      console.log('chatwoot:on-message', e.detail);
      
    });

    // 监听对话开始事件
    window.addEventListener('chatwoot:conversation-started', function() {
      console.log('Conversation started, setting label...');
    });

    // 监听错误事件
    window.addEventListener('chatwoot:error', function (e: any) {
      console.error('Chatwoot error:', e);
    });

    // 检查是否已经加载过 Chatwoot
    if (!document.querySelector('script[src*="sdk.js"]')) {
      loadChatwoot()
    }

    // 添加一个定时器，在 Chatwoot 加载后尝试设置标签
    const checkAndSetLabel = () => {
      if (window.$chatwoot) {
        try {
          window.$chatwoot.setLabel('官网用户');
          console.log('Label set via timer');
        } catch (error) {
          console.error('Error setting label via timer:', error);
        }
      } else {
        // 如果 Chatwoot 还没加载，1秒后重试
        setTimeout(checkAndSetLabel, 1000);
      }
    };

    // 5秒后开始检查
    setTimeout(checkAndSetLabel, 5000);

    // 清理函数
    return () => {
      const existingScript = document.querySelector('script[src*="sdk.js"]')
      if (existingScript) {
        existingScript.remove()
      }
      
      // 移除事件监听器
      window.removeEventListener('chatwoot:ready', function () {
        console.log('Chatwoot is ready, setting user...');
      });
      window.removeEventListener('chatwoot:on-message', function (e: any) {
        console.log('chatwoot:on-message', e.detail);
      });
      window.removeEventListener('chatwoot:conversation-started', function() {
        console.log('Conversation started, setting label...');
      });
      window.removeEventListener('chatwoot:error', function (e: any) {
        console.error('Chatwoot error:', e);
      });
    }
  }, [])

  return null // 这个组件不渲染任何可见内容
}

export default ChatwootWidget
