'use client';

import { trackCase<PERSON>lick } from '@/utils/analytics';
import Link from 'next/link';
import { ReactNode } from 'react';

interface CaseClickTrackerProps {
  caseId: number;
  caseTitle: string;
  href: string;
  source: string;
  className?: string;
  children: ReactNode;
}

export default function CaseClickTracker({
  caseId,
  caseTitle,
  href,
  source,
  className,
  children
}: CaseClickTrackerProps) {
  const handleClick = () => {
    trackCaseClick(caseId, caseTitle, source);
  };

  return (
    <Link 
      href={href} 
      className={className}
      onClick={handleClick}
    >
      {children}
    </Link>
  );
}
