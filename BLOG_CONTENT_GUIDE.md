# 博客内容规范指南

## 📋 概述

本文档为运营人员提供博客文章的内容规范，确保所有文章都能按照统一格式提供，并正确显示在网站上。

## 📝 文章内容格式

### 1. 基础信息（必需）

```typescript
{
  id: number;              // 唯一ID，不能重复
  title: string;           // 文章标题
  excerpt: string;         // 文章摘要（150-200字）
  image: string;           // 封面图片路径
  tags: string[];          // 标签数组（3-5个）
  publishDate: string;     // 发布日期（YYYY-MM-DD格式）
  status: 'published';     // 文章状态
}
```

### 2. 作者信息（必需）

```typescript
{
  author: {
    name: string;          // 作者姓名
    image: string;         // 作者头像路径
    designation: string;   // 作者职位/头衔
  }
}
```

### 3. 文章内容（必需）

```typescript
{
  content: {
    content: string;       // 完整的HTML格式文章内容
    excerpt: string;       // 文章摘要
    readingTime?: number;  // 预计阅读时间（分钟）
    sections?: BlogSection[]; // 文章章节结构（可选）
  }
}
```

## 🎯 内容要求

### 文章标题
- **长度**: 50-70个字符
- **格式**: 清晰、吸引人、包含关键词
- **示例**: "5个降低Amazon ACOS的实用策略"

### 文章摘要
- **长度**: 150-200字
- **内容**: 概括文章要点，吸引读者点击
- **格式**: 纯文本，不使用HTML标签

### 文章正文
- **格式**: HTML格式
- **长度**: 1500-3000字
- **结构**: 使用适当的标题层级（h2, h3, h4）

### 标签
- **数量**: 3-5个标签
- **格式**: 相关且具体的关键词
- **示例**: ["Amazon广告", "ACOS优化", "PPC策略", "ROI提升"]

## 📄 HTML内容格式规范

### 允许的HTML标签
```html
<!-- 标题 -->
<h2>二级标题</h2>
<h3>三级标题</h3>
<h4>四级标题</h4>

<!-- 段落 -->
<p>段落内容</p>

<!-- 列表 -->
<ul>
  <li>无序列表项</li>
</ul>
<ol>
  <li>有序列表项</li>
</ol>

<!-- 强调 -->
<strong>粗体文本</strong>
<em>斜体文本</em>

<!-- 链接 -->
<a href="https://example.com">链接文本</a>

<!-- 引用 -->
<blockquote>引用内容</blockquote>

<!-- 代码 -->
<code>代码片段</code>
<pre><code>代码块</code></pre>
```

### 内容结构示例

```html
<h2>1. 理解ACOS的重要性</h2>
<p>ACOS（Advertising Cost of Sales）是衡量亚马逊广告效果的关键指标...</p>

<h3>1.1 什么是ACOS</h3>
<p>ACOS表示广告支出占销售额的百分比...</p>

<h2>2. 优化策略</h2>
<p>以下是5个实用的ACOS优化策略：</p>

<ul>
  <li><strong>关键词优化</strong>：选择高转化率的关键词</li>
  <li><strong>出价调整</strong>：根据表现动态调整出价</li>
  <li><strong>否定关键词</strong>：排除不相关的搜索词</li>
</ul>

<h3>2.1 关键词优化详解</h3>
<p>关键词优化是降低ACOS的基础...</p>

<blockquote>
  <p>专业提示：定期分析关键词表现，及时调整策略。</p>
</blockquote>
```

## 🖼️ 图片规范

### 封面图片
- **尺寸**: 1200x630像素（16:9比例）
- **格式**: JPG或PNG
- **大小**: 小于500KB
- **路径**: `/images/blog/文章ID-cover.jpg`

### 文章内图片
- **尺寸**: 最大宽度800像素
- **格式**: JPG或PNG
- **大小**: 小于300KB
- **路径**: `/images/blog/文章ID-图片序号.jpg`

## 📊 数据文件格式

### 完整示例

```typescript
{
  id: 7,
  title: "如何通过AI优化降低Amazon ACOS",
  excerpt: "本文详细介绍如何利用人工智能技术优化亚马逊广告，有效降低ACOS并提升ROI。包含实用的策略和工具推荐。",
  image: "/images/blog/blog-07.jpg",
  author: {
    name: "张明",
    image: "/images/blog/author-04.png",
    designation: "AI广告优化专家"
  },
  tags: ["AI优化", "Amazon广告", "ACOS降低", "ROI提升", "广告策略"],
  publishDate: "2024-08-20",
  status: "published",
  content: {
    content: `
      <h2>引言</h2>
      <p>在竞争激烈的亚马逊市场中，降低ACOS是每个卖家都追求的目标...</p>
      
      <h2>AI优化的核心优势</h2>
      <p>人工智能技术为广告优化带来了革命性的变化...</p>
      
      <ul>
        <li>自动化出价调整</li>
        <li>智能关键词发现</li>
        <li>实时性能监控</li>
      </ul>
      
      <h3>具体实施步骤</h3>
      <p>以下是详细的实施步骤...</p>
    `,
    excerpt: "本文详细介绍如何利用人工智能技术优化亚马逊广告，有效降低ACOS并提升ROI。",
    readingTime: 8,
    sections: [
      {
        id: "intro",
        title: "引言",
        content: "<p>在竞争激烈的亚马逊市场中...</p>",
        level: 2
      },
      {
        id: "ai-advantages",
        title: "AI优化的核心优势",
        content: "<p>人工智能技术为广告优化...</p>",
        level: 2
      }
    ]
  }
}
```

## 🔧 技术实现

### 1. 添加新文章
1. 在 `components/Blog/blogData.ts` 中添加新的博客数据
2. 准备文章图片并放入 `public/images/blog/` 目录
3. 运行 `npm run build:blog` 重新构建
4. 运行 `npm run verify:blog` 验证生成结果

### 2. 内容验证
- 确保所有必需字段都已填写
- 验证HTML格式是否正确
- 检查图片路径是否存在
- 确认标签数量在合理范围内

### 3. SEO优化
- 标题包含目标关键词
- 摘要吸引人且包含关键信息
- 正文结构清晰，使用适当的标题层级
- 标签相关且具体

## 📞 技术支持

如有技术问题，请联系开发团队：
- 内容格式问题
- 图片上传问题
- 构建错误问题

---

**最后更新**: 2024-08-20  
**版本**: v1.0
