import Image from 'next/image';
import blogData from "@/components/Blog/blogData";
import BlogClickTracker from '@/components/BlogClickTracker';
import { Metadata } from 'next';

// 生成元数据
export const metadata: Metadata = {
  title: 'Blog - DeepBI | AI-Driven Amazon Advertising Optimization',
  description: 'Explore the latest Amazon advertising strategies, AI optimization techniques, and ecommerce growth insights. Learn how to reduce ACOS, improve ROI, and scale your Amazon business.',
  openGraph: {
    title: 'Blog - DeepBI | AI-Driven Amazon Advertising Optimization',
    description: 'Explore the latest Amazon advertising strategies, AI optimization techniques, and ecommerce growth insights. Learn how to reduce ACOS, improve ROI, and scale your Amazon business.',
    type: 'website',
  },
};



const Blog = () => {
  return (
    <>
      {/* Hero Section */}
      <section className="relative pt-32 bg-gradient-to-br from-slate-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
              Blog
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed">
              Insights, strategies, and expert knowledge to help you master Amazon advertising with AI
            </p>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {blogData.map((blog) => (
              <article
                key={blog.id}
                className="group bg-white rounded-xl overflow-hidden hover:shadow-2xl transition-all duration-300 border border-gray-100"
              >
                {/* Image */}
                <BlogClickTracker
                  blogId={blog.id}
                  blogTitle={blog.title}
                  href={`/blog/${blog.id}`}
                  source="blog_list_image"
                  className="block relative overflow-hidden"
                >
                  <div className="aspect-[16/10] relative">
                    <Image
                      src={blog.image}
                      alt={blog.title}
                      fill
                      className="group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                  {/* Tag */}
                  <div className="absolute top-4 left-4">
                    {blog.tags.map((tag) => (
                      <span key={tag} className="bg-blue-600 mr-2 text-white px-3 py-1 rounded-full text-sm font-medium">
                        {tag}
                      </span>
                    ))}
                  </div>
                </BlogClickTracker>

                {/* Content */}
                <div className="p-6">
                  <BlogClickTracker
                    blogId={blog.id}
                    blogTitle={blog.title}
                    href={`/blog/${blog.id}`}
                    source="blog_list_title"
                    className="block"
                  >
                    <h2 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors line-clamp-2">
                      {blog.title}
                    </h2>
                  </BlogClickTracker>
                  
                  <p className="text-gray-600 mb-6 line-clamp-3 leading-relaxed">
                    {blog.paragraph}
                  </p>

                  {/* Author & Date */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="relative w-10 h-10 rounded-full overflow-hidden">
                        <Image
                          src={blog.author.image}
                          alt={blog.author.name}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{blog.author.name}</p>
                        <p className="text-xs text-gray-500">{blog.author.designation}</p>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">
                      {blog.publishDate}
                    </div>
                  </div>
                </div>
              </article>
            ))}
          </div>

          {/* Load More Button */}
          <div className="text-center mt-12">
            <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
              Load More Articles
            </button>
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      {/* <section className="py-16 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Stay Updated
            </h2>
            <p className="text-gray-600 mb-8">
              Get the latest insights on Amazon advertising and AI optimization delivered to your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-600 focus:border-transparent"
              />
              <button className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </section> */}
    </>
  );
};

export default Blog;