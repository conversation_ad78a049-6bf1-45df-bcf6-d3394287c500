import './globals.css'
import type { Metadata } from 'next'
import Script from 'next/script'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import ErrorBoundary from '@/components/ErrorBoundary'
// import ChatwootWidget from '@/components/ChatwootWidget'
import { ReactNode } from 'react'
import dynamic from 'next/dynamic'
import { Inter, Poppins } from 'next/font/google'
import CookieConsent from '@/components/CookieConsent'

const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  fallback: ['system-ui', 'sans-serif']
})

const poppins = Poppins({ 
  weight: ['300', '400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
  fallback: ['system-ui', 'sans-serif']
})

// 动态导入客户端语言检测组件，避免服务器端渲染错误
const ClientLanguageDetection = dynamic(
  () => import('@/components/ClientWrapper'),
  { 
    ssr: false,
    loading: () => null
  }
)

export const metadata: Metadata = {
  title: 'DeepBI - AI-Powered Amazon Ad Assistant',
  description: 'DeepBI is an AI-powered Amazon advertising optimization tool that helps sellers lower ACOS, increase sales, and save time with intelligent automation.',
  keywords:'DeepBI, Amazon AI advertising optimization, Intelligent ad tool, ACOS reduction, AI ad assistant',
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
  },
}

export default function RootLayout({
  children,
}: {
  children: ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable}`}>
      <head>
        <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
        {/* 强制预加载 favicon */}
        <link rel="preload" href="/favicon.ico" as="image" type="image/x-icon" />
        {/* 多种 favicon 格式支持 */}
        <link rel="icon" href="/favicon.ico" type="image/x-icon" />
        <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon" />
        <link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16" />
        <link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="32x32" />
        {/* 添加缓存控制 */}
        <meta httpEquiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
        <meta httpEquiv="Pragma" content="no-cache" />
        <meta httpEquiv="Expires" content="0" />
        
        {/* Consent Mode v2 default (deny all) must run before GA config */}
        <Script id="ga-consent-default" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);} 
            gtag('consent', 'default', {
              ad_user_data: 'denied',
              ad_personalization: 'denied',
              ad_storage: 'denied',
              analytics_storage: 'denied'
            });
          `}
        </Script>
        {/* Google Analytics */}
        <Script async src="https://www.googletagmanager.com/gtag/js?id=G-Q37YGZPM6T" strategy="afterInteractive" />
        <Script id="ga-init" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);} 
            gtag('js', new Date());
            gtag('config', 'G-Q37YGZPM6T');
          `}
        </Script>
      </head>
      <body className={`bg-white ${inter.variable} ${poppins.variable}`}>
          <Header />
          <div className="flex-grow">
            {children}
          </div>
          <Footer />
          
          {/* 添加语言检测组件 */}
          <ErrorBoundary>
            <ClientLanguageDetection />
          </ErrorBoundary>
          
          {/* 添加 Chatwoot 聊天小部件 */}
          {/* <ChatwootWidget /> */}
          {/* Cookie Consent Banner */}
          <CookieConsent />
      </body>
    </html>
  )
}