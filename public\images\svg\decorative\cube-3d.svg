<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="front" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stop-color="#3b82f6" />
          <stop offset="100%" stop-color="#2563eb" />
        </linearGradient>
        <linearGradient id="top" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stop-color="#60a5fa" />
          <stop offset="100%" stop-color="#3b82f6" />
        </linearGradient>
        <linearGradient id="right" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stop-color="#1d4ed8" />
          <stop offset="100%" stop-color="#1e40af" />
        </linearGradient>
      </defs>
      
      <!-- 立方体 -->
      <polygon points="50,50 150,50 150,150 50,150" fill="url(#front)" stroke="#0f172a" stroke-width="1" transform="skewY(30) translate(0,-30)" />
      <polygon points="50,50 150,50 190,10 90,10" fill="url(#top)" stroke="#0f172a" stroke-width="1" transform="skewY(30) translate(0,-30)" />
      <polygon points="150,50 190,10 190,110 150,150" fill="url(#right)" stroke="#0f172a" stroke-width="1" transform="skewY(30) translate(0,-30)" />
      
      <!-- 反光效果 -->
      <polygon points="60,60 140,60 140,140 60,140" fill="white" opacity="0.05" transform="skewY(30) translate(0,-30)" />
      <polygon points="60,60 140,60 180,20 100,20" fill="white" opacity="0.1" transform="skewY(30) translate(0,-30)" />
    </svg>