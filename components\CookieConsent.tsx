"use client";

import { useEffect, useMemo, useState } from "react";

type ConsentStatus = "granted" | "denied";
type CategoryConsent = {
  analytics: boolean;
  ads: boolean;
};
type StoredConsent = ConsentStatus | CategoryConsent;

const STORAGE_KEY = "deepbi_cookie_consent";

function getStoredConsent(): StoredConsent | null {
  if (typeof window === "undefined") return null;
  try {
    const v = localStorage.getItem(STORAGE_KEY);
    if (v === "granted" || v === "denied") return v;
    if (!v) return null;
    const obj = JSON.parse(v);
    if (obj && typeof obj === "object" && "analytics" in obj && "ads" in obj) {
      return { analytics: !!(obj as any).analytics, ads: !!(obj as any).ads };
    }
    return null;
  } catch {
    return null;
  }
}

function setStoredConsent(status: StoredConsent) {
  try {
    if (typeof status === "string") {
      localStorage.setItem(STORAGE_KEY, status);
    } else {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(status));
    }
  } catch {}
}

function updateGtagConsent(status: StoredConsent) {
  if (typeof window === "undefined") return;
  // 兼容：字符串（全部同意/拒绝）或分类对象
  let analytics: ConsentStatus = "denied";
  let ads: ConsentStatus = "denied";
  if (typeof status === "string") {
    analytics = status === "granted" ? "granted" : "denied";
    ads = analytics;
  } else {
    analytics = status.analytics ? "granted" : "denied";
    ads = status.ads ? "granted" : "denied";
  }
  // @ts-ignore
  window.gtag?.("consent", "update", {
    analytics_storage: analytics,
    ad_storage: ads,
    ad_user_data: ads,
    ad_personalization: ads,
  });
}

export default function CookieConsent() {
  const [visible, setVisible] = useState(false);
  const [showPrefs, setShowPrefs] = useState(false);
  const [prefs, setPrefs] = useState<CategoryConsent>({ analytics: false, ads: false });

  useEffect(() => {
    const existing = getStoredConsent();
    if (!existing) {
      setVisible(true);
    } else {
      updateGtagConsent(existing);
      // 若是分类对象，填充 UI 状态
      if (typeof existing !== "string") {
        setPrefs(existing);
      } else if (existing === "granted") {
        setPrefs({ analytics: true, ads: true });
      } else {
        setPrefs({ analytics: false, ads: false });
      }
    }
  }, []);

  function acceptAll() {
    setPrefs({ analytics: true, ads: true });
    setStoredConsent("granted");
    updateGtagConsent("granted");
    setVisible(false);
  }

  function acceptNecessary() {
    setPrefs({ analytics: false, ads: false });
    setStoredConsent("denied");
    updateGtagConsent("denied");
    setVisible(false);
  }

  function savePreferences() {
    const stored: CategoryConsent = { ...prefs };
    setStoredConsent(stored);
    updateGtagConsent(stored);
    setVisible(false);
    setShowPrefs(false);
  }

  if (!visible) return null;

  return (
    <div className="fixed inset-x-0 bottom-0 z-50">
      <div className="mx-auto max-w-7xl px-4 pb-4">
        <div className="rounded-2xl bg-white shadow-xl ring-1 ring-black/5 p-4 md:p-10">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between md:space-x-6 gap-3">
            <div className="mb-1 md:mb-0">
              {/* <p className="text-sm md:text-base text-gray-800 font-medium">We use cookies to improve your experience and analyze traffic.</p> */}
              <p className="text-sm text-gray-600 mt-1">By clicking “Accept All Cookies”, you agree to the storing of cookies on your device to enhance site navigation, analyze site usage, and assist in our marketing efforts.</p>
            </div>
            <div className="flex flex-col md:flex-row items-stretch md:items-center gap-2 md:gap-3 w-full md:w-auto">
              <button
                onClick={() => setShowPrefs(true)}
                className="text-[#2563EB] underline text-[12px] mr-2 whitespace-nowrap text-center"
              >
                Cookies Settings
              </button>
              <button
                onClick={acceptNecessary}
                className="h-10 md:h-10 px-4 md:px-5 rounded-xl bg-[#2563EB] text-white text-sm shadow-md hover:shadow-lg hover:bg-[#0f2ed4] active:bg-[#0d27bb] focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-1 focus-visible:ring-[#2563EB] whitespace-nowrap min-w-[148px] w-full md:w-auto"
              >
                Reject All Cookies
              </button>
              <button
                onClick={acceptAll}
                className="h-10 md:h-10 px-4 md:px-5 rounded-xl bg-[#2563EB] text-white text-sm  shadow-md hover:shadow-lg hover:bg-[#0f2ed4] active:bg-[#0d27bb] focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-1 focus-visible:ring-[#2563EB] whitespace-nowrap min-w-[160px] w-full md:w-auto"
              >
                Accept All Cookies
              </button>
            </div>
          </div>
        </div>

        {showPrefs && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
            <div className="bg-white w-full max-w-lg mx-4 rounded-2xl shadow-2xl ring-1 ring-black/5 p-6">
              <h3 className="text-lg font-semibold text-gray-900">Cookie preferences</h3>
              <p className="text-sm text-gray-600 mt-1">Choose which categories to allow. You can change this later.</p>
              <div className="mt-5 space-y-4">
                <label className="flex items-start gap-3">
                  <input
                    type="checkbox"
                    className="mt-1 h-4 w-4"
                    checked={prefs.analytics}
                    onChange={(e) => setPrefs((p) => ({ ...p, analytics: e.target.checked }))}
                  />
                  <div>
                    <p className="font-medium text-gray-900">Analytics</p>
                    <p className="text-sm text-gray-600">Helps us understand how the site is used. No personal ads.</p>
                  </div>
                </label>
                <label className="flex items-start gap-3">
                  <input
                    type="checkbox"
                    className="mt-1 h-4 w-4"
                    checked={prefs.ads}
                    onChange={(e) => setPrefs((p) => ({ ...p, ads: e.target.checked }))}
                  />
                  <div>
                    <p className="font-medium text-gray-900">Advertising</p>
                    <p className="text-sm text-gray-600">Personalization, remarketing and ad measurement.</p>
                  </div>
                </label>
              </div>
              <div className="mt-6 flex justify-end gap-2">
                <button onClick={() => setShowPrefs(false)} className="h-10 px-4 rounded-full border border-gray-300 text-gray-800 hover:bg-gray-50">Cancel</button>
                <button onClick={savePreferences} className="h-10 px-4 rounded-full bg-[#2563EB] text-white hover:bg-[#0f2ed4]">Save preferences</button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}


