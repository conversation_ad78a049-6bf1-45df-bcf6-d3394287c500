// /**
//  * 手动构建脚本 - 跳过整个Next.js构建过程
//  * 直接创建必要的HTML文件和目录结构
//  */
// const fs = require('fs');
// const path = require('path');
// const { execSync } = require('child_process');

// console.log('======== 开始执行手动构建流程 ========');
// console.time('总构建时间');

// // 安全的文件操作函数
// const safeWriteFile = (filePath, content) => {
//   try {
//     const dir = path.dirname(filePath);
//     if (!fs.existsSync(dir)) {
//       fs.mkdirSync(dir, { recursive: true });
//     }
//     fs.writeFileSync(filePath, content);
//     return true;
//   } catch (err) {
//     console.error(`写入文件错误 ${filePath}:`, err);
//     return false;
//   }
// };

// const safeCopyFile = (src, dest) => {
//   try {
//     const dir = path.dirname(dest);
//     if (!fs.existsSync(dir)) {
//       fs.mkdirSync(dir, { recursive: true });
//     }
    
//     // 如果目标文件已存在，先尝试删除
//     if (fs.existsSync(dest)) {
//       try {
//         fs.unlinkSync(dest);
//       } catch (e) {
//         console.warn(`无法删除已存在的文件 ${dest}:`, e);
//       }
//     }
    
//     // 读取源文件内容再写入目标文件
//     const content = fs.readFileSync(src, 'utf-8');
//     fs.writeFileSync(dest, content, 'utf-8');
//     return true;
//   } catch (err) {
//     console.error(`复制文件错误 ${src} -> ${dest}:`, err);
    
//     // 尝试通过写入方式创建文件
//     try {
//       if (fs.existsSync(src)) {
//         const content = fs.readFileSync(src, 'utf-8');
//         safeWriteFile(dest, content);
//         console.log(`通过写入内容方式复制成功: ${dest}`);
//         return true;
//       }
//     } catch (e) {
//       console.error(`备选复制方法也失败:`, e);
//     }
    
//     return false;
//   }
// };

// // 确保输出目录存在
// const outDir = path.join(__dirname, '..', 'out');
// if (!fs.existsSync(outDir)) {
//   fs.mkdirSync(outDir, { recursive: true });
// }

// // 读取博客数据
// const blogJsonPath = path.join(__dirname, '..', 'public', 'data', 'blog-posts.json');
// let blogPosts = [];
// if (fs.existsSync(blogJsonPath)) {
//   try {
//     const blogData = fs.readFileSync(blogJsonPath, 'utf-8');
//     blogPosts = JSON.parse(blogData);
//     console.log(`找到 ${blogPosts.length} 篇博客文章`);
//   } catch (err) {
//     console.error('无法解析博客数据:', err);
//   }
// }

// // 创建博客页面HTML模板
// const createBlogPageHtml = () => {
//   return `<!DOCTYPE html>
// <html>
// <head>
//   <meta charset="utf-8">
//   <meta name="viewport" content="width=device-width, initial-scale=1.0">
//   <title>博客 - DeepBI</title>
//   <script>
//     // 当页面加载完成后，获取当前URL参数并处理
//     window.addEventListener('DOMContentLoaded', function() {
//       const pathSegments = window.location.pathname.split('/');
//       const localeIndex = pathSegments.indexOf('blog') - 1;
//       const locale = localeIndex >= 0 ? pathSegments[localeIndex] : 'zh';
      
//       // 如果是详情页路径，提取slug
//       let slug = '';
//       if (pathSegments.length > pathSegments.indexOf('blog') + 1) {
//         slug = pathSegments[pathSegments.indexOf('blog') + 1];
//       }
      
//       // 如果URL中已经有slug参数，优先使用它
//       const urlParams = new URLSearchParams(window.location.search);
//       const querySlug = urlParams.get('slug');
//       if (querySlug) {
//         slug = querySlug;
//       }
      
//       // 更新当前页面URL
//       if (slug && !urlParams.has('slug')) {
//         const newUrl = '/' + locale + '/blog?slug=' + slug;
//         window.history.replaceState({}, '', newUrl);
//       }
//     });
//   </script>
// </head>
// <body>
//   <div id="app">
//     <div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: Arial, sans-serif;">
//       <div style="text-align: center;">
//         <h1>正在加载博客...</h1>
//         <p>请稍候，页面正在加载中</p>
//       </div>
//     </div>
//   </div>
// </body>
// </html>`;
// };

// // 创建博客详情页HTML模板
// const createBlogDetailHtml = (post) => {
//   // 构建SEO友好的详情页模板，直接包含文章内容
//   return `<!DOCTYPE html>
// <html>
// <head>
//   <meta charset="utf-8">
//   <meta name="viewport" content="width=device-width, initial-scale=1.0">
//   <title>${post ? (post.internal_title || post.title) : '博客文章'} - DeepBI</title>
//   <meta name="description" content="${post ? post.description : ''}">
//   <meta property="og:title" content="${post ? (post.internal_title || post.title) : '博客文章'} - DeepBI">
//   <meta property="og:description" content="${post ? post.description : ''}">
//   <meta property="og:image" content="${post ? post.image : ''}">
//   <meta property="og:url" content="SITE_URL_PLACEHOLDER">
//   <link rel="canonical" href="CANONICAL_URL_PLACEHOLDER">
//   <link rel="stylesheet" href="/_next/static/css/d944134a3d5ca92d.css">
  
//   <script>
//     // 客户端交互脚本 - 仅对真实用户生效，API和爬虫不会执行
//     window.addEventListener('DOMContentLoaded', function() {
//       // 仅当用户访问页面时执行交互逻辑
//       if (!navigator.userAgent.includes('Mozilla') || navigator.userAgent.includes('bot') || navigator.userAgent.includes('crawl')) {
//         // 这是API或爬虫，不执行客户端交互
//         return;
//       }
      
//       // 加载客户端脚本进行交互增强
//       // 这里可以添加任何需要的客户端交互逻辑
//     });
//   </script>
//   <style>
//     body {
//       font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
//       line-height: 1.6;
//       color: #333;
//       max-width: 800px;
//       margin: 0 auto;
//       padding: 20px;
//     }
//     h1 {
//       font-size: 2.2rem;
//       margin-bottom: 1rem;
//     }
//     .meta {
//       font-size: 0.9rem;
//       color: #666;
//       margin-bottom: 2rem;
//     }
//     .keywords {
//       display: flex;
//       flex-wrap: wrap;
//       gap: 8px;
//       margin-bottom: 2rem;
//     }
//     .keyword {
//       background: #e6f3ff;
//       color: #0066cc;
//       padding: 4px 12px;
//       border-radius: 20px;
//       font-size: 0.85rem;
//     }
//     .description {
//       font-size: 1.1rem;
//       line-height: 1.7;
//       margin-bottom: 2rem;
//       color: #555;
//     }
//     .content {
//       margin-top: 2rem;
//     }
//     .back-link {
//       display: inline-block;
//       margin-top: 2rem;
//       color: #0066cc;
//       text-decoration: none;
//     }
//     .back-link:hover {
//       text-decoration: underline;
//     }
//     img {
//       max-width: 100%;
//       height: auto;
//     }
//   </style>
// </head>
// <body>
//   <div id="content">
//     <h1>${post ? (post.internal_title || post.title) : '加载中...'}</h1>
//     <div class="meta">
//       <span>${post ? new Date(post.date).toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' }) : ''}</span>
//       <span> • </span>
//       <span>DeepBI团队</span>
//     </div>
    
//     <div class="keywords">
//       ${post ? post.keywords.map(keyword => `<span class="keyword">${keyword}</span>`).join('') : ''}
//     </div>
    
//     <div class="description">
//       ${post ? post.description : ''}
//     </div>
    
//     <div class="content">
//       ${post && post.content ? post.content : ''}
//     </div>
    
//     <a href="../" class="back-link">« 返回文章列表</a>
//   </div>
  
//   <!-- 预加载JavaScript以提升交互体验 -->
//   <script src="/_next/static/chunks/webpack-2c02792ef4bcee20.js" defer></script>
//   <script src="/_next/static/chunks/fd9d1056-f4444b6b5c638a41.js" defer></script>
//   <script src="/_next/static/chunks/596-6190e7ef22fa7458.js" defer></script>
//   <script src="/_next/static/chunks/main-app-554af892be42106d.js" defer></script>
// </body>
// </html>`;
// };

// try {
//   // 1. 创建博客页面
//   console.log('1. 创建博客页面...');
//   const locales = ['zh', 'en', 'ja'];
  
//   locales.forEach(locale => {
//     // 为每个语言创建博客列表页面
//     const blogDir = path.join(outDir, locale, 'blog');
//     if (!fs.existsSync(blogDir)) {
//       fs.mkdirSync(blogDir, { recursive: true });
//     }
    
//     // 写入博客列表页的HTML
//     safeWriteFile(
//       path.join(blogDir, 'index.html'),
//       createBlogPageHtml()
//     );
    
//     // 为每篇博客文章创建详情页
//     blogPosts.forEach(post => {
//       // 只使用ID创建文件夹，不使用slug
//       const slug = post.id.toString();
      
//       const detailDir = path.join(blogDir, slug);
//       if (!fs.existsSync(detailDir)) {
//         fs.mkdirSync(detailDir, { recursive: true });
//       }
      
//       // 创建包含特定语言和URL的HTML
//       const postHtml = createBlogDetailHtml(post)
//         .replace('SITE_URL_PLACEHOLDER', `https://www.deepbi.cn/${locale}/blog/${post.id}`)
//         .replace('CANONICAL_URL_PLACEHOLDER', `https://www.deepbi.cn/${locale}/blog/${post.id}`);
      
//       // 写入博客详情页的HTML
//       safeWriteFile(
//         path.join(detailDir, 'index.html'),
//         postHtml
//       );
//     });
    
//     console.log(`   已创建 ${locale} 语言的博客页面`);
//   });
  
//   // 2. 复制或创建博客数据文件
//   console.log('2. 复制博客数据...');
//   const dataDir = path.join(outDir, 'data');
//   if (!fs.existsSync(dataDir)) {
//     fs.mkdirSync(dataDir, { recursive: true });
//   }
  
//   if (fs.existsSync(blogJsonPath)) {
//     const destPath = path.join(dataDir, 'blog-posts.json');
//     if (!safeCopyFile(blogJsonPath, destPath)) {
//       // 如果复制失败，直接读取内容再写入
//       try {
//         const blogData = fs.readFileSync(blogJsonPath, 'utf-8');
//         safeWriteFile(destPath, blogData);
//         console.log(`   已通过读写方式复制博客数据文件`);
//       } catch (e) {
//         console.error(`   无法复制博客数据文件:`, e);
//       }
//     }
//   }
  
//   // 3. 创建或更新robots.txt文件
//   console.log('3. 创建robots.txt文件...');
//   const robotsContent = `# https://www.robotstxt.org/robotstxt.html
// User-agent: *
// Allow: /

// # Sitemap
// Sitemap: https://www.deepbi.cn/sitemap.xml
// `;

//   safeWriteFile(
//     path.join(outDir, 'robots.txt'),
//     robotsContent
//   );
  
//   // 4. 创建简单的sitemap.xml
//   console.log('4. 创建sitemap.xml文件...');
//   const currentDate = new Date().toISOString().split('T')[0];
//   let sitemapContent = `<?xml version="1.0" encoding="UTF-8"?>
// <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
//   <url>
//     <loc>https://www.deepbi.cn/</loc>
//     <lastmod>${currentDate}</lastmod>
//     <changefreq>weekly</changefreq>
//     <priority>1.0</priority>
//   </url>`;
  
//   // 为每个语言添加主页和关键页面
//   locales.forEach(locale => {
//     sitemapContent += `
//   <url>
//     <loc>https://www.deepbi.cn/${locale}</loc>
//     <lastmod>${currentDate}</lastmod>
//     <changefreq>weekly</changefreq>
//     <priority>0.9</priority>
//   </url>
//   <url>
//     <loc>https://www.deepbi.cn/${locale}/blog</loc>
//     <lastmod>${currentDate}</lastmod>
//     <changefreq>weekly</changefreq>
//     <priority>0.8</priority>
//   </url>
//   <url>
//     <loc>https://www.deepbi.cn/${locale}/about</loc>
//     <lastmod>${currentDate}</lastmod>
//     <changefreq>monthly</changefreq>
//     <priority>0.7</priority>
//   </url>`;
    
//     // 为每篇博客文章添加URL
//     blogPosts.forEach(post => {
//       sitemapContent += `
//   <url>
//     <loc>https://www.deepbi.cn/${locale}/blog/${post.slug || post.id}</loc>
//     <lastmod>${currentDate}</lastmod>
//     <changefreq>monthly</changefreq>
//     <priority>0.6</priority>
//   </url>`;
//     });
//   });
  
//   sitemapContent += `
// </urlset>`;

//   safeWriteFile(
//     path.join(outDir, 'sitemap.xml'),
//     sitemapContent
//   );
  
//   console.log('5. 构建完成!');
//   console.timeEnd('总构建时间');
//   console.log('======== 手动构建流程执行完毕 ========');
  
// } catch (error) {
//   console.error('手动构建过程中发生错误:', error);
//   process.exit(1);
// } 