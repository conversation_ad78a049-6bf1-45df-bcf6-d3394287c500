# -*- coding: utf-8 -*-
"""压缩文件夹图片"""
import os
from PIL import Image
import sys


def is_image(file_path):
    """判断是否是图片"""
    if os.path.exists(file_path):
        ext = os.path.splitext(file_path)[1].lower()  # 获取文件扩展名并转为小写
        return ext in ['.png', '.jpg', '.jpeg']  # 判断是否是 png 或 jpg 格式
    return False

def resize_if_needed(image_path, max_dimension=16383):
    """检查并调整图片尺寸"""
    try:
        with Image.open(image_path) as img:
            width, height = img.size
            # 可配置的最长边，默认 16383（兼容 Chrome 渲染限制）
            max_dimension = int(max_dimension)
            
            if width > max_dimension or height > max_dimension:
                # 计算缩放比例
                ratio = min(max_dimension / width, max_dimension / height)
                new_width = int(width * ratio)
                new_height = int(height * ratio)
                
                # 创建新图片
                resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # 保存调整后的图片
                resized_img.save(image_path)
                print(f"已调整图片尺寸: {image_path}")
                return True
    except Exception as e:
        print(f"处理图片时出错: {image_path}, 错误: {str(e)}")
    return False

# 压缩图片


def deal_pic(from_path: str, to_path: str, quality: int = 50, max_dimension: int = 16383):
    """压缩图片函数"""
    if not os.path.exists(to_path):
        os.makedirs(to_path, exist_ok=True)

    if not os.path.exists(from_path) or not os.path.isdir(from_path):
        print('文件夹不存在')
        return

    for fname in os.listdir(from_path):
        son_path = from_path + '/' + fname

        if is_image(son_path):
            new_to_path = to_path + '/' + fname
            # 先检查并调整图片尺寸
            resize_if_needed(son_path, max_dimension=max_dimension)
            # 保持原文件名和扩展名，但用 cwebp 压缩
            gen_cmd = f"cwebp \"{son_path}\" -q {int(quality)} -o \"{new_to_path}\""
            print(gen_cmd)
            os.system(gen_cmd)
            continue

        if not os.path.exists(son_path) or not os.path.isdir(son_path):
            continue
        new_to_path = to_path + '/' + fname
        if not os.path.exists(new_to_path):
            os.makedirs(new_to_path, exist_ok=True)
        deal_pic(son_path, new_to_path, quality=quality, max_dimension=max_dimension)


if __name__ == '__main__':
    # 用法：python compression_dir_big_pic.py <from_path> <to_path> [quality] [max_dimension]
    # 默认 from_path=./out, to_path=./build, quality=50, max_dimension=16383
    from_path = sys.argv[1] if len(sys.argv) > 1 else './out'
    to_path = sys.argv[2] if len(sys.argv) > 2 else './build'
    quality = int(sys.argv[3]) if len(sys.argv) > 3 else 50
    max_dim = int(sys.argv[4]) if len(sys.argv) > 4 else 16383
    print(f"开始压缩图片: from={from_path}, to={to_path}, q={quality}, max={max_dim}")
    deal_pic(from_path, to_path, quality=quality, max_dimension=max_dim)
