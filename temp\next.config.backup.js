/** @type {import('next').NextConfig} */
const nextConfig = {
    output: 'export',
    images: {
        unoptimized: true, 
        domains: ['www.deepbi.cn'],
        remotePatterns: [
          {
            protocol: 'https',
            hostname: 'www.deepbi.cn',
            pathname: '/**',
          },
        ],
    },
    // 在静态导出中标记某些页面为完全客户端渲染
    // 这会创建一个空的HTML shell，所有内容在客户端加载
    experimental: {
      clientRouting: true,
    },
    skipMiddlewareUrlNormalize: true,
    skipTrailingSlashRedirect: true,
}

module.exports = nextConfig
