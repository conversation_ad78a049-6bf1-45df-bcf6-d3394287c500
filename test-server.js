const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 8080;

// 静态文件中间件配置
const staticOptions = {
  maxAge: '1d',
  etag: false,
  lastModified: false
};

// 主站静态文件 (out/ 目录)
app.use('/', express.static(path.join(__dirname, 'out'), staticOptions));

// 文档站静态文件 (my-app/out/ 目录)
app.use('/docs', express.static(path.join(__dirname, 'my-app/out'), staticOptions));

// 处理主站的 SPA 路由
app.get('*', (req, res, next) => {
  // 如果是 /docs 路径，跳过主站处理
  if (req.path.startsWith('/docs')) {
    return next();
  }
  
  // 检查文件是否存在
  const filePath = path.join(__dirname, 'out', req.path);
  
  // 尝试直接文件
  if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
    return res.sendFile(filePath);
  }
  
  // 尝试 .html 文件
  const htmlPath = filePath + '.html';
  if (fs.existsSync(htmlPath)) {
    return res.sendFile(htmlPath);
  }
  
  // 尝试目录下的 index.html
  const indexPath = path.join(filePath, 'index.html');
  if (fs.existsSync(indexPath)) {
    return res.sendFile(indexPath);
  }
  
  // 返回主站的 404 页面
  const notFoundPath = path.join(__dirname, 'out', '404.html');
  if (fs.existsSync(notFoundPath)) {
    return res.status(404).sendFile(notFoundPath);
  }
  
  res.status(404).send('Page not found');
});

// 处理文档站的 SPA 路由
app.get('/docs/*', (req, res) => {
  const docsPath = req.path.replace('/docs', '') || '/';
  const filePath = path.join(__dirname, 'my-app/out', docsPath);
  
  // 尝试直接文件
  if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
    return res.sendFile(filePath);
  }
  
  // 尝试 .html 文件
  const htmlPath = filePath + '.html';
  if (fs.existsSync(htmlPath)) {
    return res.sendFile(htmlPath);
  }
  
  // 尝试目录下的 index.html
  const indexPath = path.join(filePath, 'index.html');
  if (fs.existsSync(indexPath)) {
    return res.sendFile(indexPath);
  }
  
  // 返回文档站的 404 页面
  const docsNotFoundPath = path.join(__dirname, 'my-app/out', '404.html');
  if (fs.existsSync(docsNotFoundPath)) {
    return res.status(404).sendFile(docsNotFoundPath);
  }
  
  res.status(404).send('Docs page not found');
});

app.listen(PORT, () => {
  console.log(`🚀 测试服务器启动成功！`);
  console.log(`📱 主站: http://localhost:${PORT}`);
  console.log(`📚 文档: http://localhost:${PORT}/docs`);
  console.log(`\n📁 目录结构:`);
  console.log(`   / → out/ (主站)`);
  console.log(`   /docs → my-app/out/ (文档站)`);
  console.log(`\n按 Ctrl+C 停止服务器`);
});
