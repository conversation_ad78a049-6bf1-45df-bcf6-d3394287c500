'use client';

import React, { useState } from 'react';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import CryptoJS from 'crypto-js';

export default function EmailSubscription() {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState('');

  // 加密签名生成函数
  const generateSignature = (data: Record<string, string>, timestamp: number, nonce: string) => {
    // 将data对象转为JSON字符串，确保键值对按字母顺序排序
    const sortedData: Record<string, string> = {};
    Object.keys(data).sort().forEach(key => {
      sortedData[key] = data[key];
    });
    const dataStr = JSON.stringify(sortedData, null, 0); // 确保没有多余的空格

    // API KEY - 实际环境中应该从环境变量获取
    const API_KEY = process.env.NEXT_PUBLIC_API_KEY || 'default_api_key';

    // 直接拼接所有参数
    const signStr = `${dataStr}${timestamp}${nonce}${API_KEY}`;

    // 使用 SHA256 生成签名
    return CryptoJS.SHA256(signStr).toString();
  };

  // API调用函数
  const postEmailData = async () => {
    try {
      const newValues = {
        email: email,
        tag: "官网上线前预约"
      };

      // 将newValues进行base64编码，处理中文字符
      const jsonString = JSON.stringify(newValues);
      // 使用兼容的方法处理UTF-8字符的base64编码
      const utf8Bytes = new TextEncoder().encode(jsonString);
      let binaryString = '';
      for (let i = 0; i < utf8Bytes.length; i++) {
        binaryString += String.fromCharCode(utf8Bytes[i]);
      }
      const encodedData = btoa(binaryString);

      const timestamp = new Date().getTime();
      const nonce = uuidv4();
      const signature = generateSignature(newValues, timestamp, nonce);

      const res = await axios.post('//atlas.deepbi.cn/api/market_cuse', {
        data: encodedData,
        timestamp,
        nonce,
        signature,
      });

      if (res.data.code === 200) {
        setIsSubmitted(true);
        setEmail('');
        setError('');
      } else {
        setError(res.data.msg || 'Subscription failed. Please try again.');
      }
    } catch (error) {
      console.error('Email subscription error:', error);
      setError('Something went wrong. Please try again.');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email) {
      setError('Please enter your email address');
      return;
    }

    if (!isValidEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      await postEmailData();
    } catch (err) {
      setError('Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const isValidEmail = (email: string) => {
    // 更严格的邮箱验证正则表达式
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

    // 基本格式验证
    if (!emailRegex.test(email)) {
      return false;
    }

    // 长度限制
    if (email.length > 254) {
      return false;
    }

    // 本地部分长度限制
    const localPart = email.split('@')[0];
    if (localPart.length > 64) {
      return false;
    }

    return true;
  };

  if (isSubmitted) {
    return (
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
          <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-white mb-2">Thank you for subscribing!</h3>
        <p className="text-blue-100 mb-6">We'll notify you as soon as DeepBI goes live.</p>
        <button
          onClick={() => setIsSubmitted(false)}
          className="text-blue-200 hover:text-white underline transition-colors"
        >
          Subscribe another email
        </button>
      </div>
    );
  }

  return (
    <div className="text-center">
      <p className="text-sm sm:text-base md:text-lg lg:text-xl text-blue-100 mb-8 sm:mb-12 max-w-2xl mx-auto leading-relaxed px-4">
        Get notified when DeepBI launches. Be the first to experience AI-powered Amazon advertising optimization.
      </p>
      
      <form onSubmit={handleSubmit} className="max-w-md mx-auto">
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex-1">
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address"
              className="w-full px-4 py-3 rounded-xl border-2 border-white/30 bg-white/10 text-white placeholder-white/60 focus:outline-none focus:border-white/50 focus:bg-white/20 transition-all duration-300 backdrop-blur-sm"
              disabled={isSubmitting}
            />
            {error && (
              <p className="text-red-300 text-sm mt-2 text-left">{error}</p>
            )}
          </div>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-8 py-3 bg-white text-blue-600 rounded-xl font-semibold hover:bg-blue-50 transform hover:scale-105 transition-all duration-300 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Subscribing...
              </div>
            ) : (
              'Notify Me'
            )}
          </button>
        </div>
      </form>
      
      {/* <p className="text-xs text-blue-200/80 mt-4 max-w-sm mx-auto">
        We respect your privacy. Unsubscribe at any time.
      </p> */}
    </div>
  );
}
