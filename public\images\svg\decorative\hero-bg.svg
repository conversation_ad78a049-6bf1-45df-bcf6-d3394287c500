<svg width="1200" height="600" viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="hero-bg" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stop-color="#1e40af" />
          <stop offset="100%" stop-color="#1d4ed8" />
        </linearGradient>
        <linearGradient id="grid-fade" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stop-color="#3b82f6" stop-opacity="0.1" />
          <stop offset="100%" stop-color="#3b82f6" stop-opacity="0" />
        </linearGradient>
      </defs>
      
      <!-- 背景 -->
      <rect x="0" y="0" width="1200" height="600" fill="url(#hero-bg)" />
      
      <!-- 网格线 -->
      <g opacity="0.2">
        <rect x="0" y="0" width="1200" height="600" fill="url(#grid-fade)" />
        <line x1="0" y1="0" x2="1200" y2="0" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="20" x2="1200" y2="20" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="40" x2="1200" y2="40" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="60" x2="1200" y2="60" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="80" x2="1200" y2="80" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="100" x2="1200" y2="100" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="120" x2="1200" y2="120" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="140" x2="1200" y2="140" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="160" x2="1200" y2="160" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="180" x2="1200" y2="180" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="200" x2="1200" y2="200" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="220" x2="1200" y2="220" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="240" x2="1200" y2="240" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="260" x2="1200" y2="260" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="280" x2="1200" y2="280" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="300" x2="1200" y2="300" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="320" x2="1200" y2="320" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="340" x2="1200" y2="340" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="360" x2="1200" y2="360" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="380" x2="1200" y2="380" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="400" x2="1200" y2="400" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="420" x2="1200" y2="420" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="440" x2="1200" y2="440" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="460" x2="1200" y2="460" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="480" x2="1200" y2="480" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="500" x2="1200" y2="500" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="520" x2="1200" y2="520" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="540" x2="1200" y2="540" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="560" x2="1200" y2="560" stroke="#3b82f6" stroke-width="0.5" /><line x1="0" y1="580" x2="1200" y2="580" stroke="#3b82f6" stroke-width="0.5" />
        <line x1="0" y1="0" x2="0" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="20" y1="0" x2="20" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="40" y1="0" x2="40" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="60" y1="0" x2="60" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="80" y1="0" x2="80" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="100" y1="0" x2="100" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="120" y1="0" x2="120" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="140" y1="0" x2="140" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="160" y1="0" x2="160" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="180" y1="0" x2="180" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="200" y1="0" x2="200" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="220" y1="0" x2="220" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="240" y1="0" x2="240" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="260" y1="0" x2="260" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="280" y1="0" x2="280" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="300" y1="0" x2="300" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="320" y1="0" x2="320" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="340" y1="0" x2="340" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="360" y1="0" x2="360" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="380" y1="0" x2="380" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="400" y1="0" x2="400" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="420" y1="0" x2="420" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="440" y1="0" x2="440" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="460" y1="0" x2="460" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="480" y1="0" x2="480" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="500" y1="0" x2="500" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="520" y1="0" x2="520" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="540" y1="0" x2="540" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="560" y1="0" x2="560" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="580" y1="0" x2="580" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="600" y1="0" x2="600" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="620" y1="0" x2="620" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="640" y1="0" x2="640" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="660" y1="0" x2="660" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="680" y1="0" x2="680" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="700" y1="0" x2="700" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="720" y1="0" x2="720" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="740" y1="0" x2="740" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="760" y1="0" x2="760" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="780" y1="0" x2="780" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="800" y1="0" x2="800" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="820" y1="0" x2="820" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="840" y1="0" x2="840" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="860" y1="0" x2="860" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="880" y1="0" x2="880" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="900" y1="0" x2="900" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="920" y1="0" x2="920" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="940" y1="0" x2="940" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="960" y1="0" x2="960" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="980" y1="0" x2="980" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="1000" y1="0" x2="1000" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="1020" y1="0" x2="1020" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="1040" y1="0" x2="1040" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="1060" y1="0" x2="1060" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="1080" y1="0" x2="1080" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="1100" y1="0" x2="1100" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="1120" y1="0" x2="1120" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="1140" y1="0" x2="1140" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="1160" y1="0" x2="1160" y2="600" stroke="#3b82f6" stroke-width="0.5" /><line x1="1180" y1="0" x2="1180" y2="600" stroke="#3b82f6" stroke-width="0.5" />
      </g>
      
      <!-- 装饰性圆形 -->
      <circle cx="200" cy="150" r="40" fill="#3b82f6" opacity="0.1" />
      <circle cx="200" cy="150" r="30" fill="#3b82f6" opacity="0.1" />
      <circle cx="200" cy="150" r="20" fill="#3b82f6" opacity="0.2" />
      
      <circle cx="1000" cy="400" r="60" fill="#3b82f6" opacity="0.1" />
      <circle cx="1000" cy="400" r="45" fill="#3b82f6" opacity="0.1" />
      <circle cx="1000" cy="400" r="30" fill="#3b82f6" opacity="0.2" />
      
      <!-- 装饰性线条 -->
      <path d="M0,400 C200,350 400,450 600,400 S1000,300 1200,400" stroke="#3b82f6" stroke-width="2" fill="none" opacity="0.2" />
      <path d="M0,420 C200,370 400,470 600,420 S1000,320 1200,420" stroke="#60a5fa" stroke-width="1" fill="none" opacity="0.15" />
    </svg>