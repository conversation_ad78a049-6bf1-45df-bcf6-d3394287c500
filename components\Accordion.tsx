'use client';

import React, { useState } from 'react';

interface AccordionItem {
  question: string;
  answer: string[];
}

interface AccordionProps {
  items: AccordionItem[];
  itemsPerPage?: number;
}

const Accordion: React.FC<AccordionProps> = ({ items, itemsPerPage = 8 }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  // Get current page items
  const getCurrentItems = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return items.slice(startIndex, startIndex + itemsPerPage);
  };

  // Calculate total pages
  const totalPages = Math.ceil(items.length / itemsPerPage);

  // Toggle accordion item
  const toggleItem = (index: number) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Accordion Items */}
      <div className="space-y-4">
        {getCurrentItems().map((item, index) => (
          <div 
            key={index} 
            className={`overflow-hidden rounded-lg border ${activeIndex === index ? 'border-gray-500 bg-[#1b2b41]' : 'border-transparent bg-white'}`}
          >
            {/* Question Header */}
            <button
              onClick={() => toggleItem(index)}
              className={`w-full text-left p-3 md:p-5 flex justify-between items-center ${activeIndex === index ? 'text-white' : 'text-gray-800'}`}
            >
              <span className="font-semibold text-base md:text-lg">{item.question}</span>
              <div className={`flex items-center justify-center w-8 h-8 rounded ${activeIndex === index ? 'bg-[#334155] text-white' : 'bg-[#F8FAFC] text-gray-600'}`}>
                {activeIndex === index ? (
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                )}
              </div>
            </button>
            
            {/* Answer Content */}
            {activeIndex === index && (
              <div className="p-3 md:p-5 bg-[#1b2b41] text-white">
                <div className="text-sm md:text-base space-y-2">
                  {item.answer.map((paragraph, i) => (
                    <p key={i}>{paragraph}</p>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex space-x-2">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
              <button
                key={page}
                onClick={() => {
                  setCurrentPage(page);
                  setActiveIndex(null);
                }}
                className={`w-8 h-8 flex items-center justify-center rounded ${
                  currentPage === page
                    ? 'bg-[#1e64fa] text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-100'
                }`}
              >
                {page}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Accordion; 