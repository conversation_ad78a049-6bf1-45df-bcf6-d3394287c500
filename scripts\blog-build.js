const fs = require('fs');
const path = require('path');

console.log('======== 博客页面构建验证 ========');

try {
  // 检查博客数据文件
  const blogDataPath = path.join(__dirname, '..', 'components', 'Blog', 'blogData.ts');
  console.log('1. 检查博客数据文件...');
  
  if (!fs.existsSync(blogDataPath)) {
    console.error('   ❌ 博客数据文件不存在:', blogDataPath);
    process.exit(1);
  }
  
  console.log('   ✓ 博客数据文件存在');
  
  // 读取博客数据
  const blogDataContent = fs.readFileSync(blogDataPath, 'utf-8');
  const blogIds = [];
  
  // 提取博客ID
  const idMatches = blogDataContent.match(/id:\s*(\d+)/g);
  if (idMatches) {
    idMatches.forEach(match => {
      const id = match.replace('id:', '').trim();
      blogIds.push(id);
    });
  }
  
  console.log(`   ✓ 找到 ${blogIds.length} 篇博客文章`);
  
  // 检查out目录中的博客页面
  const outDir = path.join(__dirname, '..', 'out');
  const blogDir = path.join(outDir, 'blog');
  
  console.log('2. 检查构建输出...');
  
  if (!fs.existsSync(outDir)) {
    console.error('   ❌ out目录不存在，请先运行构建命令');
    process.exit(1);
  }
  
  if (!fs.existsSync(blogDir)) {
    console.error('   ❌ blog目录不存在，博客页面可能未正确生成');
    process.exit(1);
  }
  
  // 检查博客列表页
  const blogIndexPath = path.join(blogDir, 'index.html');
  if (fs.existsSync(blogIndexPath)) {
    console.log('   ✓ 博客列表页已生成');
  } else {
    console.error('   ❌ 博客列表页未生成');
  }
  
  // 检查博客详情页
  console.log('3. 检查博客详情页...');
  let generatedPages = 0;
  
  blogIds.forEach(id => {
    const detailDir = path.join(blogDir, id);
    const detailIndexPath = path.join(detailDir, 'index.html');
    
    if (fs.existsSync(detailIndexPath)) {
      generatedPages++;
      console.log(`   ✓ 博客详情页 ${id} 已生成`);
    } else {
      console.error(`   ❌ 博客详情页 ${id} 未生成`);
    }
  });
  
  console.log(`   ✓ 成功生成 ${generatedPages}/${blogIds.length} 个博客详情页`);
  
  // 检查sitemap
  console.log('4. 检查SEO文件...');
  const sitemapPath = path.join(outDir, 'sitemap.xml');
  const robotsPath = path.join(outDir, 'robots.txt');
  
  if (fs.existsSync(sitemapPath)) {
    console.log('   ✓ sitemap.xml 已生成');
  } else {
    console.error('   ❌ sitemap.xml 未生成');
  }
  
  if (fs.existsSync(robotsPath)) {
    console.log('   ✓ robots.txt 已生成');
  } else {
    console.error('   ❌ robots.txt 未生成');
  }
  
  console.log('======== 博客构建验证完成 ========');
  
  if (generatedPages === blogIds.length) {
    console.log('✅ 所有博客页面已成功生成！');
  } else {
    console.log('⚠️  部分博客页面生成失败，请检查构建日志');
  }
  
} catch (error) {
  console.error('博客构建验证出错:', error);
  process.exit(1);
} 
