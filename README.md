# DeepBI Official Website

DeepBI Official Website is a multi-language static website built with Next.js, showcasing DeepBI's products and services. DeepBI specializes in providing AI-powered advertising optimization solutions for Amazon cross-border sellers.

## Project Features

- 🌐 **Multi-language Support**: English, Chinese, and Japanese
- 🚀 **Static Site Export**: Fast loading, easy to deploy
- 📱 **Responsive Design**: Optimized for mobile and desktop
- 🎨 **Modern UI**: Built with Tailwind CSS
- 🔒 **Secure Encryption**: Using crypto-js for sensitive data
- 📝 **Blog System**: Multi-language blog posts with custom URLs

## Key Functions

- **Multi-language Routing**: Language switching based on URL path (`/en/`, `/zh/`, `/ja/`)
- **Product Introduction**: Detailed showcase of DeepBI's intelligent advertising assistant
- **Case Studies**: Success stories of DeepBI on the Amazon platform
- **Pricing Plans**: Flexible subscription plans with price fetching from API
- **User Feedback**: Integrated feedback form for user opinions
- **Blog System**: Support for article lists and detail pages with custom URL formats

## Tech Stack

- **Framework**: Next.js 13.4.12
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Internationalization**: next-intl
- **Component Library**: Custom components + Iconify
- **HTTP Client**: Axios
- **Notifications**: react-hot-toast
- **Encryption**: crypto-js

## Project Structure

```
/
├── app/                   # Next.js App Router directory
│   ├── [locale]/          # Multi-language routes
│   │   ├── page.tsx       # Homepage
│   │   ├── about/         # About us
│   │   ├── blog/          # Blog pages
│   │   ├── charge/        # Pricing plans
│   │   ├── feedback/      # User feedback
│   │   ├── privacy/       # Privacy policy
│   │   └── service/       # Terms of service
│   ├── globals.css        # Global styles
│   └── layout.tsx         # Global layout
├── components/            # Reusable components
├── messages/              # Internationalization messages
│   ├── en.json            # English
│   ├── zh.json            # Chinese
│   └── ja.json            # Japanese
├── public/                # Static assets
│   └── data/              # Static data files
│       └── blog-posts.json # Blog post data
├── scripts/               # Custom scripts
│   └── manual-build.js    # Manual blog page building script
├── next.config.js         # Next.js configuration
├── tailwind.config.js     # Tailwind CSS configuration
└── package.json           # Project dependencies
```

## Quick Start

### Install Dependencies

```bash
npm install
```

### Development Server

```bash
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to view the website.

### Build the Application

The project provides various build options:

**Standard Build**
```bash
npm run build:only
```

### Test the Built Application

```bash
npx serve@latest out
```
