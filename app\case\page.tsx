import Image from 'next/image';
import caseData from '@/components/Case/caseData';
import { Metadata } from 'next';
import CaseClickTracker from '@/components/CaseClickTracker';

export const metadata: Metadata = {
  title: 'Cases - DeepBI | Real Results & Success Stories',
  description: 'Explore real Amazon advertising optimization cases with proven results. Learn from successful strategies for ACOS reduction, ROI improvement, and campaign optimization.',
  openGraph: {
    title: 'Cases - DeepBI | Real Results & Success Stories',
    description: 'Explore real Amazon advertising optimization cases with proven results.',
    type: 'website',
  },
};

export default function CaseList() {
  return (
    <>
      <section className="relative pt-32 bg-gradient-to-br from-slate-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">Cases</h1>
            <p className="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed">Real results and actionable strategies from successful campaigns</p>
          </div>
        </div>
      </section>

      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {caseData.map((item) => (
              <article key={item.id} className="group bg-white rounded-xl overflow-hidden hover:shadow-2xl transition-all duration-300 border border-gray-100">
                <CaseClickTracker
                  caseId={item.id}
                  caseTitle={item.title}
                  href={`/case/${item.id}`}
                  source="case_list_image"
                  className="block relative overflow-hidden"
                >
                  <div className="aspect-[16/10] relative">
                    <Image src={item.image} alt={item.title} fill className="object-cover group-hover:scale-105 transition-transform duration-300" />
                  </div>
                  <div className="absolute top-4 left-4">
                    <span className="bg-emerald-600 text-white px-3 py-1 rounded-full text-sm font-medium">{item.tags[0]}</span>
                  </div>
                </CaseClickTracker>
                <div className="p-6">
                  <CaseClickTracker
                    caseId={item.id}
                    caseTitle={item.title}
                    href={`/case/${item.id}`}
                    source="case_list_title"
                    className="block"
                  >
                    <h2 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors line-clamp-2">{item.title}</h2>
                  </CaseClickTracker>
                  <p className="text-gray-600 mb-6 line-clamp-3 leading-relaxed">{item.paragraph}</p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="relative w-10 h-10 rounded-full overflow-hidden">
                        <Image src={item.author.image} alt={item.author.name} fill className="object-cover" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{item.author.name}</p>
                        <p className="text-xs text-gray-500">{item.author.designation}</p>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500">{item.publishDate}</div>
                  </div>
                </div>
              </article>
            ))}
          </div>
        </div>
      </section>
    </>
  );
}


