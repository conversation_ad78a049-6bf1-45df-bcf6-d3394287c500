import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Blog - DeepBI | AI-Driven Amazon Advertising Optimization',
  description: 'Explore the latest Amazon advertising strategies, AI optimization techniques, and ecommerce growth insights. Learn how to reduce ACOS, improve ROI, and scale your Amazon business.',
  openGraph: {
    title: 'Blog - DeepBI | AI-Driven Amazon Advertising Optimization',
    description: 'Explore the latest Amazon advertising strategies, AI optimization techniques, and ecommerce growth insights. Learn how to reduce ACOS, improve ROI, and scale your Amazon business.',
    type: 'website',
  },
};

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
