# DeepBI官网GA4事件追踪实现文档

## 📋 概述

为DeepBI官网实现了完整的Google Analytics 4 (GA4) 事件追踪功能，用于监控用户在网站各个功能模块的行为数据，包括博客、案例、邮箱订阅和语言切换等。

## 🎯 追踪的事件类型

### 1. 博客点击事件 (`blog_click`)
**触发场景：**
- 用户点击博客列表页面的博客图片
- 用户点击博客列表页面的博客标题
- 用户点击博客详情页面的相关文章链接

**事件参数：**
```javascript
{
  event: 'blog_click',
  event_category: 'Blog',
  event_label: '博客标题',
  blog_id: 博客ID,
  blog_title: '博客标题',
  click_source: '点击来源',
  custom_parameter_1: 'blog_博客ID'
}
```

**点击来源类型：**
- `blog_list_image` - 博客列表页图片点击
- `blog_list_title` - 博客列表页标题点击
- `related_posts` - 相关文章点击

### 2. 博客页面浏览事件 (`blog_view`)
**触发场景：**
- 用户访问博客详情页面时自动触发

**事件参数：**
```javascript
{
  event: 'blog_view',
  event_category: 'Blog',
  event_label: '博客标题',
  blog_id: 博客ID,
  blog_title: '博客标题',
  page_title: '博客标题'
}
```

### 3. 博客菜单点击事件 (`blog_menu_click`)
**触发场景：**
- 用户点击网站头部导航的"Blog"菜单
- 用户点击移动端菜单的"Blog"选项

**事件参数：**
```javascript
{
  event: 'blog_menu_click',
  event_category: 'Navigation',
  event_label: 'Header Blog Menu' 或 'Mobile Blog Menu',
  click_source: 'header_menu' 或 'mobile_menu'
}
```

### 4. 案例点击事件 (`case_click`)
**触发场景：**
- 用户点击案例列表页面的案例图片
- 用户点击案例列表页面的案例标题
- 用户点击案例详情页面的相关案例链接

**事件参数：**
```javascript
{
  event: 'case_click',
  event_category: 'Case',
  event_label: '案例标题',
  case_id: 案例ID,
  case_title: '案例标题',
  click_source: '点击来源',
  custom_parameter_1: 'case_案例ID'
}
```

**点击来源类型：**
- `case_list_image` - 案例列表页图片点击
- `case_list_title` - 案例列表页标题点击
- `related_cases` - 相关案例点击

### 5. 案例页面浏览事件 (`case_view`)
**触发场景：**
- 用户访问案例详情页面时自动触发

**事件参数：**
```javascript
{
  event: 'case_view',
  event_category: 'Case',
  event_label: '案例标题',
  case_id: 案例ID,
  case_title: '案例标题',
  page_title: '案例标题'
}
```

### 6. 案例菜单点击事件 (`case_menu_click`)
**触发场景：**
- 用户点击网站头部导航的"Cases"菜单
- 用户点击移动端菜单的"Cases"选项

**事件参数：**
```javascript
{
  event: 'case_menu_click',
  event_category: 'Navigation',
  event_label: 'Header Cases Menu' 或 'Mobile Cases Menu',
  click_source: 'header_menu' 或 'mobile_menu'
}
```

### 7. 邮箱订阅事件
#### 7.1 订阅开始 (`email_subscription_start`)
**触发场景：**
- 用户点击"Notify Me"按钮开始邮箱订阅

**事件参数：**
```javascript
{
  event: 'email_subscription_start',
  event_category: 'Email',
  event_label: 'Email Subscription Started',
  email_domain: '邮箱域名',
  subscription_source: 'homepage_footer'
}
```

#### 7.2 订阅成功 (`email_subscription_success`)
**触发场景：**
- 邮箱订阅API调用成功

**事件参数：**
```javascript
{
  event: 'email_subscription_success',
  event_category: 'Email',
  event_label: 'Email Subscription Success',
  email_domain: '邮箱域名',
  subscription_source: 'homepage_footer',
  tag: '官网上线前预约'
}
```

#### 7.3 订阅失败 (`email_subscription_failed`)
**触发场景：**
- 邮箱订阅API调用失败

**事件参数：**
```javascript
{
  event: 'email_subscription_failed',
  event_category: 'Email',
  event_label: 'Email Subscription Failed',
  error_message: '错误信息',
  subscription_source: 'homepage_footer'
}
```

### 8. 语言重定向弹窗事件
#### 8.1 关闭弹窗 (`language_modal_close`)
**触发场景：**
- 用户点击语言重定向弹窗的关闭按钮

**事件参数：**
```javascript
{
  event: 'language_modal_close',
  event_category: 'Language',
  event_label: 'Close Language Redirect Modal',
  action: 'close_modal'
}
```

#### 8.2 跳转中文站 (`language_modal_redirect`)
**触发场景：**
- 用户点击"立即前往"按钮跳转到中文站

**事件参数：**
```javascript
{
  event: 'language_modal_redirect',
  event_category: 'Language',
  event_label: 'Redirect to Chinese Site',
  action: 'redirect_to_chinese',
  destination: 'https://www.deepbi.cn'
}
```

## 📊 数据分析价值

### 用户行为洞察
1. **内容偏好分析** - 通过博客点击数据了解用户最感兴趣的内容类型
2. **用户路径追踪** - 分析用户从博客列表到详情页的转化路径
3. **相关内容效果** - 评估相关文章推荐的点击效果
4. **移动端vs桌面端** - 对比不同设备上的用户行为差异

### 内容优化指导
1. **热门内容识别** - 找出点击率最高的博客文章
2. **内容布局优化** - 分析图片vs标题点击的偏好
3. **相关推荐优化** - 根据相关文章点击数据优化推荐算法
4. **导航优化** - 分析Blog菜单的使用情况

## 🔧 技术实现

### 核心文件
1. **`utils/analytics.ts`** - GA4事件追踪工具函数
2. **`app/blog/page.tsx`** - 博客列表页面追踪
3. **`app/blog/[id]/page.tsx`** - 博客详情页面追踪
4. **`app/case/page.tsx`** - 案例列表页面追踪
5. **`app/case/[id]/page.tsx`** - 案例详情页面追踪
6. **`components/Header.tsx`** - 导航菜单追踪
7. **`components/EmailSubscription.tsx`** - 邮箱订阅追踪
8. **`components/LanguageRedirectModal.tsx`** - 语言重定向弹窗追踪
9. **`components/BlogClickTracker.tsx`** - 博客点击追踪组件
10. **`components/BlogViewTracker.tsx`** - 博客浏览追踪组件
11. **`components/CaseClickTracker.tsx`** - 案例点击追踪组件
12. **`components/CaseViewTracker.tsx`** - 案例浏览追踪组件

### 关键函数
```typescript
// 博客相关追踪
trackBlogClick(blogId, blogTitle, source)
trackBlogView(blogId, blogTitle)

// 案例相关追踪
trackCaseClick(caseId, caseTitle, source)
trackCaseView(caseId, caseTitle)

// 通用事件追踪
trackEvent(eventName, parameters)
```

## 📈 GA4报告建议

### 推荐创建的自定义报告

1. **博客内容表现报告**
   - 维度：blog_title, click_source
   - 指标：事件计数, 用户数
   - 筛选器：event_name = 'blog_click'

2. **博客转化漏斗报告**
   - 步骤1：blog_menu_click (导航点击)
   - 步骤2：blog_view (页面浏览)
   - 步骤3：blog_click (内容点击)

3. **设备类型对比报告**
   - 维度：设备类别, click_source
   - 指标：事件计数, 转化率

### 关键指标监控

1. **博客点击率** - blog_click事件数 / blog_view事件数
2. **相关文章点击率** - related_posts点击数 / 博客页面浏览数
3. **导航转化率** - blog_view数 / blog_menu_click数
4. **热门内容排行** - 按blog_id统计的点击次数排序

## 🚀 后续优化建议

### 短期优化（1-2周）
1. **阅读时长追踪** - 实现博客阅读时长统计
2. **滚动深度追踪** - 监控用户阅读文章的完成度
3. **搜索行为追踪** - 如果有博客搜索功能，添加搜索事件追踪

### 中期优化（1个月）
1. **A/B测试支持** - 为不同的博客布局添加测试标识
2. **用户分群分析** - 基于博客行为对用户进行分群
3. **内容推荐优化** - 基于GA4数据优化相关文章推荐算法

### 长期优化（3个月）
1. **预测分析** - 基于历史数据预测内容表现
2. **个性化推荐** - 结合用户行为数据实现个性化内容推荐
3. **ROI分析** - 分析博客内容对业务转化的贡献

## 📝 注意事项

1. **数据隐私** - 所有追踪都遵循GDPR和相关隐私法规
2. **性能影响** - 事件追踪对页面性能影响极小
3. **数据准确性** - 建议定期验证追踪数据的准确性
4. **报告延迟** - GA4数据通常有24-48小时的处理延迟

## 🔍 验证方法

### 开发环境验证
1. 打开浏览器开发者工具
2. 查看Console日志中的GA4事件信息
3. 使用GA4 DebugView实时查看事件

### 生产环境验证
1. 使用GA4实时报告查看事件
2. 检查自定义维度和指标是否正确记录
3. 验证事件参数是否完整传递

---

**实施日期：** 2025年1月
**负责人：** 开发团队
**联系人：** 如有问题请联系开发团队
