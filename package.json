{"name": "next-i18n-static", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build --debug && node post-build.js", "build:next": "set NODE_OPTIONS=--max-old-space-size=4096 --trace-warnings && set NEXT_DEBUG=1 && next build --debug", "build:only": "next build && node post-build.js", "build:blog": "next build && node post-build.js && node scripts/blog-build.js && node scripts/build-docs-simple.js", "verify:blog": "node scripts/blog-build.js", "validate:content": "node scripts/content-validator.js", "start": "next start", "lint": "next lint", "serve": "node test-server.js", "test-server": "node test-server.js"}, "dependencies": {"@iconify/react": "^6.0.0", "@types/node": "20.4.5", "@types/react": "18.2.17", "@types/react-dom": "18.2.7", "aos": "^2.3.4", "autoprefixer": "10.4.14", "axios": "^1.9.0", "crypto-js": "^4.2.0", "eslint": "8.46.0", "eslint-config-next": "13.4.12", "framer-motion": "^12.23.12", "next": "13.4.12", "next-intl": "^2.19.0", "postcss": "8.4.27", "react": "18.2.0", "react-dom": "18.2.0", "react-hot-toast": "^2.5.2", "swiper": "^11.2.6", "tailwindcss": "3.3.3", "typescript": "5.1.6", "uuid": "^11.1.0"}, "devDependencies": {"@types/aos": "^3.0.7", "@types/crypto-js": "^4.2.2", "@types/uuid": "^10.0.0"}}