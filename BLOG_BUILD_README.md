# 博客页面构建配置说明

## 概述

本项目已配置了完整的博客页面静态生成功能，支持以下特性：

- ✅ 博客列表页静态生成 (`/blog/`)
- ✅ 博客详情页静态生成 (`/blog/[id]/`)
- ✅ SEO优化（元数据、sitemap、robots.txt）
- ✅ 构建验证脚本

## 构建命令

### 1. 标准构建（包含博客页面）
```bash
npm run build:only
```

### 2. 博客专用构建（包含验证）
```bash
npm run build:blog
```

### 3. 仅验证博客构建
```bash
npm run verify:blog
```

## 配置详情

### Next.js 配置 (`next.config.js`)
- `output: 'export'` - 启用静态导出
- `trailingSlash: true` - 添加尾部斜杠，确保URL一致性
- `images.unoptimized: true` - 静态导出时禁用图片优化

### 博客页面配置

#### 博客列表页 (`app/blog/page.tsx`)
- 自动生成元数据
- 支持SEO优化
- 静态生成

#### 博客详情页 (`app/blog/[id]/page.tsx`)
- `generateStaticParams()` - 为所有博客文章生成静态路径
- `generateMetadata()` - 为每篇文章生成SEO元数据
- 支持Open Graph标签

### 构建后处理 (`post-build.js`)
- 生成 `robots.txt`
- 生成 `sitemap.xml`（包含所有博客页面）
- 验证博客页面生成状态

## 博客数据结构

博客数据存储在 `components/Blog/blogData.ts` 中：

```typescript
interface Blog {
  id: number;
  title: string;
  paragraph: string;
  image: string;
  author: {
    name: string;
    image: string;
    designation: string;
  };
  tags: string[];
  publishDate: string;
}
```

## 生成的静态文件结构

构建完成后，`out` 目录结构如下：

```
out/
├── blog/
│   ├── index.html          # 博客列表页
│   ├── 1/
│   │   └── index.html      # 博客文章1详情页
│   ├── 2/
│   │   └── index.html      # 博客文章2详情页
│   └── ...
├── sitemap.xml             # 网站地图
└── robots.txt              # 搜索引擎爬虫配置
```

## SEO优化

### 自动生成的SEO元素
- 页面标题和描述
- Open Graph标签
- 结构化数据
- 网站地图
- Robots.txt

### 博客页面URL结构
- 列表页：`https://www.deepbi.com/blog/`
- 详情页：`https://www.deepbi.com/blog/[id]/`

## 故障排除

### 常见问题

1. **博客页面未生成**
   - 检查 `blogData.ts` 文件是否存在
   - 确认 `generateStaticParams` 函数正确配置

2. **构建失败**
   - 检查 TypeScript 类型错误
   - 确认所有导入路径正确

3. **SEO文件缺失**
   - 确认 `post-build.js` 脚本正常运行
   - 检查文件权限

### 验证步骤

运行验证脚本检查构建状态：
```bash
npm run verify:blog
```

## 添加新博客文章

1. 在 `components/Blog/blogData.ts` 中添加新的博客数据
2. 运行构建命令：`npm run build:blog`
3. 验证新页面是否正确生成：`npm run verify:blog`

## 注意事项

- 确保所有博客图片路径正确
- 博客ID必须唯一
- 构建时会自动为所有博客文章生成静态页面
- 修改博客数据后需要重新构建
