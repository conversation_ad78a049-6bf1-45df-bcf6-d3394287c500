'use client';

import { trackBlogClick } from '@/utils/analytics';
import Link from 'next/link';
import { ReactNode } from 'react';

interface BlogClickTrackerProps {
  blogId: number;
  blogTitle: string;
  href: string;
  source: string;
  className?: string;
  children: ReactNode;
}

export default function BlogClickTracker({
  blogId,
  blogTitle,
  href,
  source,
  className,
  children
}: BlogClickTrackerProps) {
  const handleClick = () => {
    trackBlogClick(blogId, blogTitle, source);
  };

  return (
    <Link 
      href={href} 
      className={className}
      onClick={handleClick}
    >
      {children}
    </Link>
  );
}
