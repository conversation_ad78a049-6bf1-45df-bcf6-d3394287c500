@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom color classes */
@layer components {
  .text-body-color {
    @apply text-gray-600;
  }
  
  .text-body-color-dark {
    @apply text-gray-300;
  }
  
  .bg-gray-dark {
    @apply bg-gray-900;
  }
}

/* 自定义滚动条样式 */
@layer base {
    /* 适用于WebKit浏览器（Chrome, Safari等） */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
  
    ::-webkit-scrollbar-track {
      background: #f5f5f5;
      border-radius: 4px;
    }
  
    ::-webkit-scrollbar-thumb {
      background: #c0c0c0;
      border-radius: 4px;
      transition: all 0.3s ease;
    }
  
    ::-webkit-scrollbar-thumb:hover {
      background: #909090;
    }
  
    /* Firefox 滚动条样式 */
    * {
      scrollbar-width: thin;
      scrollbar-color: #c0c0c0 #f5f5f5;
    }
  
    /* IE 滚动条样式 */
    * {
      -ms-overflow-style: none;
    }
  
    /* 针对暗模式的滚动条样式 */
    @media (prefers-color-scheme: dark) {
      ::-webkit-scrollbar-track {
        background: #2d3748;
      }
  
      ::-webkit-scrollbar-thumb {
        background: #4a5568;
      }
  
      ::-webkit-scrollbar-thumb:hover {
        background: #5a6678;
      }
  
      * {
        scrollbar-color: #4a5568 #2d3748;
      }
    }
  }
  
  /* Add padding to main content area to account for fixed header */
  .flex-grow {
    /* padding-top: 72px;  */
  }
  
  /* Global font settings */
:root {
  --font-family-sans: var(--font-inter), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-family-heading: var(--font-poppins), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

@layer base {
  html {
    font-family: var(--font-family-sans);
  }
  
  body {
    margin: 0;
    padding: 0;
    font-family: var(--font-family-sans);
  }
  
  /* Headers use Poppins */
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    scroll-margin-top: 100px; /* 为所有标题添加滚动边距，避免被固定header遮挡 */
  }
  
  /* 针对文章内容的锚点跳转优化 */
  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    scroll-margin-top: 100px;
  }
  
  /* 覆盖prose样式，让标题更突出 */
  .prose h1 {
    font-size: 2.5rem !important;
    font-weight: 800 !important;
    color: #1f2937 !important;
    margin-top: 3rem !important;
    margin-bottom: 1.5rem !important;
  }
  
  .prose h2 {
    font-size: 1.75rem !important;
    font-weight: 700 !important;
    color: #1f2937 !important;
    margin-top: 2.5rem !important;
    margin-bottom: 1.25rem !important;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.75rem;
  }
  
  .prose h3 {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin-top: 2rem !important;
    margin-bottom: 1rem !important;
  }
  .prose h4 {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    color: #374151 !important;
    margin-top: 1.5rem !important;
    margin-bottom: 0.5rem !important;
    margin-left: 0.75rem !important;
  }
  
  /* 增加段落间距 */
  .prose p {
    margin-top: 1.25rem !important;
    margin-bottom: 1.25rem !important;
    line-height: 1.7 !important;
  }
  
  /* 增加列表间距 */
  .prose ul, .prose ol {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    padding-left: 1rem !important;
  }
  
  /* 确保ul显示小圆点 */
  .prose ul {
    list-style-type: disc !important;
    list-style-position: outside !important;
  }
  
  /* 确保ol显示数字 */
  .prose ol {
    list-style-type: decimal !important;
    list-style-position: outside !important;
  }
  
  .prose li {
    margin-top: 0.75rem !important;
    margin-bottom: 0.75rem !important;
    line-height: 1.6 !important;
    display: list-item !important;
  }
  
  /* 增加列表项之间的间距 */
  .prose ul li, .prose ol li {
    padding-left: 0.5rem !important;
  }
  
  /* Buttons use Poppins */
  button, .btn, a.btn {
    font-family: var(--font-family-heading);
  }
}
  
/* 富文本编辑器样式支持 */
.quill-content {
  /* 基础样式 */
  line-height: 1.6;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Quill编辑器的对齐方式 */
.quill-content .ql-align-center {
  text-align: center;
}

.quill-content .ql-align-right {
  text-align: right;
}

.quill-content .ql-align-justify {
  text-align: justify;
}

/* 支持嵌入图片 */
.quill-content img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 1em auto;
  border-radius: 0.375rem;
}

/* 标题样式优化 */
.quill-content h1, .quill-content h2, .quill-content h3, 
.quill-content h4, .quill-content h5, .quill-content h6 {
  font-weight: 700;
  line-height: 1.3;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.quill-content p{
  margin: 1em 0;
}

.quill-content h1 {
  font-size: 2em;
}


.quill-content h2 {
  font-size: 1.75em;
}

.quill-content h3 {
  font-size: 1.5em;
}

/* 列表样式 */
.quill-content ul, .quill-content ol {
  padding-left: 2em;
  margin: 1em 0;
}

.quill-content ul {
  list-style-type: disc;
}

.quill-content ol {
  list-style-type: decimal;
}

/* 引用块 */
.quill-content blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1em;
  margin-left: 0;
  font-style: italic;
  color: #4a5568;
}

/* 代码块 */
.quill-content pre {
  background-color: #f7fafc;
  border-radius: 0.375rem;
  padding: 1em;
  overflow-x: auto;
  margin: 1em 0;
}

.quill-content code {
  font-family: Menlo, Monaco, Consolas, "Liberation Mono", monospace;
  font-size: 0.9em;
  background-color: #f7fafc;
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
}
  