# Cookie 同意（Consent Mode v2）功能说明（面向产品）

本文解释网站上的 Cookie 弹窗是做什么的、为什么需要、它如何工作、以及常见问题。

## 1. 这是什么？
- 我们在网站底部展示一个 Cookie 同意条幅（Banner），并提供一个“偏好设置（Preferences）”面板。
- 目的是在用户授权前，遵守隐私法规（如 GDPR/ePrivacy），合理控制分析/广告相关的 Cookie 与数据采集。

参考开源交互样式：`orestbida/cookieconsent`（仅样式/交互参考，不直接引入）
- 项目主页：[cookieconsent GitHub](https://github.com/orestbida/cookieconsent)

## 2. 为什么需要？
- 我们已接入 Google Analytics（GA）。在欧盟/英国/EEA 等地区，GA 属于“非必要 Cookie/跟踪”，需“事前同意”。
- 没有同意就默认拒绝，可降低合规风险，提升用户透明度与信任。

## 3. 按钮含义与默认策略
- Cookies Settings：打开“偏好设置”面板，用户可分别勾选“Analytics（分析）”“Advertising（广告）”。
- Reject All Cookies：仅保留必要 Cookie，拒绝分析/广告类 Cookie 与个性化跟踪。
- Accept All Cookies：同意所有可选 Cookie（分析与广告）。

默认状态：拒绝（denied）。

## 4. 同意状态与对 GA 的影响
我们采用 Google Consent Mode v2。关键点：
- 同意（granted）：
  - Google Analytics/Ads 可以写入/读取 Cookie，进行会话、归因、再营销等完整测量。
- 拒绝（denied）：
  - 不写不读分析/广告类 Cookie；可能仍有“无 Cookie 的匿名请求（cookieless pings）”用于聚合建模，不含用户标识。

技术上，我们会根据用户选择，更新以下位：
- analytics_storage（分析）
- ad_storage / ad_user_data / ad_personalization（广告）

## 5. 交互行为（用户视角）
- 首次访问：看到条幅；若不操作，则默认拒绝。
- 点击“Accept All Cookies”：立即切换为同意；刷新后不会再弹出。
- 点击“Reject All Cookies”：保持拒绝；刷新后不会再弹出。
- 点击“Cookies Settings”：进入偏好面板，可分别勾选“Analytics / Advertising”，保存后生效。

## 6. 存储位置与重置
- 本地存储键：`deepbi_cookie_consent`
  - 值可能为：`"granted"`、`"denied"`，或分类对象 `{ analytics: boolean, ads: boolean }`
- 重置方式：在浏览器控制台执行 `localStorage.removeItem('deepbi_cookie_consent')`，刷新页面即可再次弹出条幅。

## 7. 文件位置
- 组件：`components/CookieConsent.tsx`
  - 负责 UI、存储、与 GA Consent Mode v2 同步
- 注入位置：`app/layout.tsx`
  - 先注入“默认拒绝”脚本，再加载 GA，再初始化；并挂载 `<CookieConsent />`

## 8. 文案与样式调整
- 条幅与面板文案可在 `components/CookieConsent.tsx` 内直接修改。
- 主按钮颜色使用品牌蓝，可按需要调整 Tailwind 类（如 `bg-[#2563EB]`）。
- 移动端：按钮在小屏下自动折行纵向排列，宽度铺满；桌面端横向排列。

## 9. 验收与自测清单
- [ ] 首次访问出现条幅；操作后记住选择，不再弹出。
- [ ] 拒绝时，浏览器 Cookies 中不出现 `_ga`、`_ga_*` 等 GA Cookie。
- [ ] 同意后，刷新页面可看到 `_ga` Cookie 出现，GA 正常采集。
- [ ] 偏好面板能分别控制“Analytics / Advertising”，并即时影响 GA 的 consent 位。

## 10. 常见问题（FAQ）
- Q：未同意时为什么 Network 里仍看到 Google 请求？
  - A：那是 Consent Mode 的“匿名 ping”，不含 Cookie/用户标识，仅用于聚合建模，合规允许。
- Q：能否做到“完全不发任何 GA 请求直到同意”？
  - A：可以改为“延迟加载 GA 脚本”。当前采用的是官方推荐的 Consent Mode 方式，兼顾数据与合规。
- Q：支持多语言吗？
  - A：目前文案为英文，后续可以接入多语言资源文件（`messages/*.json`）或按浏览器语言显示。

---
如需切换为“更严格的零请求模式”或接入更多分类（如功能类、性能类等），请在产品方案中提出，我方可扩展组件逻辑与 UI。
