import { Metadata } from 'next';
import blogData from '@/components/Blog/blogData';

interface BlogLayoutProps {
  children: React.ReactNode;
  params: { id: string };
}

// 生成元数据
export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const post = blogData.find((post) => post.id.toString() === params.id);
  
  if (!post) {
    return {
      title: 'Blog Not Found | DeepBI',
      description: '',
    };
  }

  // 使用自定义元数据或默认值
  const metaTitle = post.content?.metaTitle || post.title;
  const metaDescription = post.content?.metaDescription || post.content?.excerpt || post.paragraph;

  return {
    title: `${metaTitle} - DeepBI Blog`,
    description: metaDescription,
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      images: [post.image],
      type: 'article',
    },
  };
}

export default function BlogDetailLayout({ children }: BlogLayoutProps) {
  return children;
}
