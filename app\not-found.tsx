'use client'
import Link from 'next/link'
import Image from 'next/image'
import { useEffect, useState } from 'react'

// 简单的多语言文本配置
const messages = {
  zh: {
    title: '404',
    subtitle: '页面未找到',
    description: '抱歉，您访问的页面不存在或已被移除。',
    backHome: '返回首页'
  },
  en: {
    title: '404',
    subtitle: 'Page Not Found',
    description: 'Sorry, the page you are looking for does not exist or has been removed.',
    backHome: 'Back to Home'
  },
  ja: {
    title: '404',
    subtitle: 'ページが見つかりません',
    description: '申し訳ございません。お探しのページは存在しないか、削除されています。',
    backHome: 'ホームに戻る'
  }
}

// 检测浏览器语言
function detectLanguage(): keyof typeof messages {
  if (typeof window === 'undefined') return 'en'
  
  const userLanguages = navigator.languages || [navigator.language]
  
  for (const lang of userLanguages) {
    const langCode = lang.toLowerCase()
    if (langCode.startsWith('zh')) return 'zh'
    if (langCode.startsWith('en')) return 'en'
    if (langCode.startsWith('ja')) return 'ja'
  }
  
  return 'en' // 默认中文
}

export default function NotFound() {
    const [locale, setLocale] = useState<keyof typeof messages>('en')
    
    useEffect(() => {
        setLocale(detectLanguage())
    }, [])
    
    const t = messages[locale]
    
    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 px-4">
            <div className="text-center max-w-md">
                <Image 
                    src="/images/deepbi-logo.png" 
                    alt="DeepBI Logo" 
                    width={120} 
                    height={40}
                    className="mx-auto mb-8"
                />
                <h1 className="text-6xl font-bold text-gray-800 mb-4">{t.title}</h1>
                <h2 className="text-2xl font-semibold text-gray-700 mb-4">{t.subtitle}</h2>
                <p className="text-gray-600 mb-8">
                    {t.description}
                </p>
                <div className="space-y-4">
                    <Link 
                        href="/" 
                        className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition duration-150"
                    >
                        {t.backHome}
                    </Link>
                    
                    {/* 语言切换按钮 */}
                    {/* <div className="flex justify-center space-x-2 mt-4">
                        {Object.keys(messages).map((lang) => (
                            <button
                                key={lang}
                                onClick={() => setLocale(lang as keyof typeof messages)}
                                className={`px-3 py-1 text-sm rounded ${
                                    locale === lang 
                                        ? 'bg-blue-600 text-white' 
                                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                                } transition duration-150`}
                            >
                                {lang.toUpperCase()}
                            </button>
                        ))}
                    </div> */}
                </div>
            </div>
        </div>
    )
}