import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import caseData from '@/components/Case/caseData';
import { Metadata } from 'next';
import CaseClickTracker from '@/components/CaseClickTracker';
import CaseViewTracker from '@/components/CaseViewTracker';

export async function generateStaticParams() {
  return caseData.map((item) => ({ id: item.id.toString() }));
}

export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const item = caseData.find((c) => c.id.toString() === params.id);
  if (!item) {
    return { title: 'Case Not Found | DeepBI', description: '' };
  }
  const metaTitle = item.content?.metaTitle || item.title;
  const metaDescription = item.content?.metaDescription || item.content?.excerpt || item.paragraph;
  return {
    title: `${metaTitle} - DeepBI Case`,
    description: metaDescription,
    openGraph: { title: metaTitle, description: metaDescription, images: [item.image], type: 'article' },
  };
}

export default function CaseDetail({ params }: { params: { id: string } }) {
  const item = caseData.find((c) => c.id === parseInt(params.id));
  if (!item) notFound();

  const articleContent = item.content?.content || item.paragraph;
  const excerpt = item.content?.excerpt || item.paragraph;
  const readingTime = item.content?.readingTime || 5;
  const sections = item.content?.sections || [];
  const relatedIds = item.content?.relatedCases || [];
  const related = relatedIds.length > 0 ? caseData.filter(x => relatedIds.includes(x.id)) : caseData.filter(x => x.id !== item.id).slice(0, 3);

  return (
    <>
      {/* 添加页面浏览追踪 */}
      <CaseViewTracker caseId={item.id} caseTitle={item.title} />

      <section className="pt-32 pb-8">
        <div className="container mx-auto px-4">
          <Link href="/case" className="inline-flex items-center text-emerald-600 hover:text-emerald-800 font-medium mb-8 group">
            <svg className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" /></svg>
            Back to Cases
          </Link>
        </div>
      </section>

      <section className="pb-8">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <div className="flex flex-wrap gap-2 mb-6">
              {item.tags.map((tag, i) => (
                <span key={i} className="bg-emerald-100 text-emerald-700 px-3 py-1 rounded-full text-sm font-medium">{tag}</span>
              ))}
            </div>
            <h1 className="text-3xl font-bold mb-6" style={{ lineHeight: '1.5' }}>{item.title}</h1>
            <div className="flex items-center space-x-6 mb-8 text-gray-600">
              <div className="flex items-center space-x-3">
                <div className="relative w-12 h-12 rounded-full overflow-hidden">
                  <Image src={item.author.image} alt={item.author.name} fill className="object-cover" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">{item.author.name}</p>
                  <p className="text-sm text-gray-600">{item.author.designation}</p>
                </div>
              </div>
              <div className="flex items-center space-x-4 text-sm">
                <span>{item.publishDate}</span>
                <span>•</span>
                <span>{readingTime} min read</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="pb-12">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <div className="aspect-[16/9] relative rounded-2xl overflow-hidden">
              <Image src={item.image} alt={item.title} fill className="object-cover" />
            </div>
          </div>
        </div>
      </section>



      <section className="pb-16 relative">
        <div className="container mx-auto px-4">
          <div className="max-w-5xl mx-auto">
            <div className="prose prose-lg prose-gray max-w-none">
              {/* 案例摘要 */}
              <div className="bg-emerald-50 p-6 rounded-xl mb-8">
                <p className="text-lg text-gray-700 leading-relaxed mb-0">
                  {excerpt}
                </p>
              </div>
              
              {/* 案例主体内容 */}
              <div 
                className="space-y-6 text-gray-700 leading-relaxed"
                dangerouslySetInnerHTML={{ __html: articleContent }}
              />
            </div>
          </div>
        </div>

        {/* 目录侧边栏 - 绝对定位到右侧 */}
        {sections.length > 0 && (
          <div className="hidden 2xl:block absolute top-0 right-14 w-80">
            <div className="sticky top-12">
              <div className="bg-gray-50 p-6 rounded-xl">
                <h2 className="text-lg font-bold text-gray-900 mb-4">On this page</h2>
                <nav>
                  <ul className="space-y-2">
                    {sections.map((section) => (
                      <li key={section.id}>
                        <a
                          href={`#${section.id}`}
                          className="text-emerald-700 hover:text-emerald-900 text-sm block py-1"
                          style={{ paddingLeft: `${(section.level - 2) * 12}px` }}
                        >
                          {section.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                </nav>
              </div>
            </div>
          </div>
        )}
      </section>

      {related.length > 0 && (
        <section className="py-16 bg-slate-50">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Related Cases</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {related.map(rc => (
                  <article key={rc.id} className="group">
                    <CaseClickTracker
                      caseId={rc.id}
                      caseTitle={rc.title}
                      href={`/case/${rc.id}`}
                      source="related_cases"
                      className="block"
                    >
                      <div className="bg-white rounded-xl overflow-hidden hover:shadow-xl transition-all duration-300">
                        <div className="aspect-[16/10] relative">
                          <Image src={rc.image} alt={rc.title} fill className="object-cover group-hover:scale-105 transition-transform duration-300" />
                        </div>
                        <div className="p-6">
                          <span className="text-emerald-700 text-sm font-medium">{rc.tags[0]}</span>
                          <h3 className="text-lg font-bold text-gray-900 mt-2 mb-3 group-hover:text-emerald-700 transition-colors line-clamp-2">{rc.title}</h3>
                          <p className="text-gray-600 text-sm line-clamp-3">{rc.content?.excerpt || rc.paragraph}</p>
                        </div>
                      </div>
                    </CaseClickTracker>
                  </article>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}
    </>
  );
}


