import Link from "next/link";

const Breadcrumb = ({
  pageName,
  description,
}: {
  pageName: string;
  description?: string;
}) => {
  return (
    <>
      <section className="overflow-hidden text-center py-10 md:py-12 lg:py-20">
        <div className="container mx-auto">
          <div className="mx-4 flex flex-wrap">
            <div className="w-full px-4">
              <div className="mx-auto max-w-[800px] text-center">
                <h1 className="mb-5 text-3xl font-bold leading-tight text-gray-800 sm:text-4xl sm:leading-tight md:text-5xl md:leading-tight">
                  {pageName}
                </h1>
                {description && (
                  <p className="mb-12 text-base !leading-relaxed text-gray-600 sm:text-lg md:text-xl">
                    {description}
                  </p>
                )}
             
              </div>
            </div>
          </div>
        </div>

    
      </section>
    </>
  );
};

export default Breadcrumb;
