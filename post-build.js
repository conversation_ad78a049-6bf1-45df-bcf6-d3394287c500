const fs = require('fs');
const path = require('path');

// 构建后处理：增强SEO
console.log('======== 开始执行构建后处理 ========');
console.time('总处理时间');

try {
  // 确保out目录存在
  console.log('1. 检查out目录...');
  if (!fs.existsSync('out')) {
    console.log('   输出目录不存在，创建out目录...');
    fs.mkdirSync('out', { recursive: true });
  }
  console.log('   ✓ out目录检查完成');

  // 读取博客数据
  console.log('2. 处理博客数据...');
  const blogDataPath = path.join(__dirname, 'components', 'Blog', 'blogData.ts');
  if (fs.existsSync(blogDataPath)) {
    console.log('   ✓ 找到博客数据文件');
  } else {
    console.log('   ! 未找到博客数据文件');
  }

  // 创建或更新robots.txt文件
  console.log('3. 创建robots.txt文件...');
  const robotsContent = `# https://www.robotstxt.org/robotstxt.html
User-agent: *
Allow: /

# Sitemap
Sitemap: https://www.deepbi.com/sitemap.xml
`;

  fs.writeFileSync(
    path.join(__dirname, 'out', 'robots.txt'),
    robotsContent
  );
  console.log('   ✓ 已创建robots.txt文件');

  // 创建增强的sitemap.xml
  console.log('4. 创建sitemap.xml文件...');
  const currentDate = new Date().toISOString().split('T')[0];
  let sitemapContent = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://www.deepbi.com/</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://www.deepbi.com/blog/</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
  </url>
  <url>
    <loc>https://www.deepbi.com/case/</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
  </url>
  <url>
    <loc>https://www.deepbi.com/about/</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
`;

  // 添加博客详情页到sitemap
  console.log('5. 添加博客详情页到sitemap...');
  try {
    // 从blogData.ts文件中提取博客ID
    const blogDataContent = fs.readFileSync(blogDataPath, 'utf-8');
    const blogIds = [];
    
    // 简单的正则表达式匹配来提取博客ID
    const idMatches = blogDataContent.match(/id:\s*(\d+)/g);
    if (idMatches) {
      idMatches.forEach(match => {
        const id = match.replace('id:', '').trim();
        blogIds.push(id);
      });
    }
    
    console.log(`   找到 ${blogIds.length} 篇博客文章`);
    
    blogIds.forEach(id => {
      sitemapContent += `  <url>
    <loc>https://www.deepbi.com/blog/${id}/</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>
`;
    });
    
    console.log('   ✓ 已添加博客详情页到sitemap');
  } catch (error) {
    console.log('   ! 处理博客数据时出错:', error.message);
  }

  // 添加案例详情页到sitemap
  console.log('6. 添加案例详情页到sitemap...');
  try {
    const caseDataPath = path.join(__dirname, 'components', 'Case', 'caseData.ts');
    if (fs.existsSync(caseDataPath)) {
      const caseDataContent = fs.readFileSync(caseDataPath, 'utf-8');
      const caseIdMatches = caseDataContent.match(/id:\s*(\d+)/g) || [];
      const caseIds = caseIdMatches.map(m => m.replace('id:', '').trim());
      console.log(`   找到 ${caseIds.length} 个案例`);
      caseIds.forEach(id => {
        sitemapContent += `  <url>\n    <loc>https://www.deepbi.com/case/${id}/</loc>\n    <lastmod>${currentDate}</lastmod>\n    <changefreq>monthly</changefreq>\n    <priority>0.7</priority>\n  </url>\n`;
      });
    } else {
      console.log('   ! 未找到案例数据文件');
    }
  } catch (error) {
    console.log('   ! 处理案例数据时出错:', error.message);
  }

  sitemapContent += '</urlset>\n';

  fs.writeFileSync(
    path.join(__dirname, 'out', 'sitemap.xml'),
    sitemapContent
  );
  console.log('   ✓ 已创建sitemap.xml文件');

  // 验证博客页面是否正确生成
  console.log('7. 验证博客页面生成...');
  const blogDir = path.join(__dirname, 'out', 'blog');
  if (fs.existsSync(blogDir)) {
    const blogFiles = fs.readdirSync(blogDir);
    console.log(`   在out/blog目录中找到 ${blogFiles.length} 个文件/目录`);
    
    // 检查是否有博客详情页目录
    const blogDetailDirs = blogFiles.filter(file => {
      const fullPath = path.join(blogDir, file);
      return fs.statSync(fullPath).isDirectory();
    });
    
    console.log(`   找到 ${blogDetailDirs.length} 个博客详情页目录`);
    
    if (blogDetailDirs.length > 0) {
      console.log('   ✓ 博客详情页已正确生成');
    } else {
      console.log('   ! 未找到博客详情页目录');
    }
  } else {
    console.log('   ! out/blog目录不存在');
  }

  console.timeEnd('总处理时间');
  console.log('======== SEO优化处理完成 ========');
} catch (error) {
  console.error('构建后处理出错:', error);
  process.exit(1);
} 