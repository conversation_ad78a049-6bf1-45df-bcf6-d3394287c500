import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';

interface LanguageRedirectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCloseWithDelay: () => void;
  onRedirect: () => void;
}

const LanguageRedirectModal: React.FC<LanguageRedirectModalProps> = ({ 
  isOpen, 
  onClose,
  onCloseWithDelay,
  onRedirect 
}) => {
  // 添加动画状态
  const [animate, setAnimate] = useState(false);

  // 控制动画效果
  useEffect(() => {
    if (isOpen) {
      // 延迟一帧触发动画
      requestAnimationFrame(() => {
        setAnimate(true);
      });
    } else {
      setAnimate(false);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden px-4">
      {/* 背景蒙层 - 移除了onClick事件 */}
      <div 
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${animate ? 'opacity-60' : 'opacity-0'}`}
      ></div>
      
      {/* 弹窗内容 */}
      <div 
        className={`bg-white rounded-lg p-6 max-w-sm w-full mx-auto relative z-10 shadow-xl 
          transition-all duration-300 transform
          ${animate ? 'scale-100 opacity-100' : 'scale-90 opacity-0'}`}
      >
        {/* 关闭按钮 - 使用onClose (简单关闭，下次显示) */}
        <button 
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-400 hover:text-gray-600 transition-colors"
          aria-label="关闭"
        >
          <Icon icon="mdi:close" className="text-xl" />
        </button>

        {/* 图标 */}
        <div className="flex justify-center mb-5">
          <div className="bg-blue-50 p-3 rounded-full">
            <Icon icon="mdi:translate" className="text-blue-600 text-2xl" />
          </div>
        </div>

        {/* 标题 */}
        <h2 className="text-xl font-bold mb-2 text-gray-800 text-center">
          检测到您的浏览器语言为中文
        </h2>
        
        {/* 描述 */}
        <p className="mb-6 text-gray-600 text-center text-sm">
          是否前往中文版网站以获得更好的浏览体验？
        </p>
        
        {/* 按钮区域 */}
        <div className="flex justify-center space-x-3">
          {/* <button
            onClick={onCloseWithDelay}
            className="px-5 py-2 border border-gray-200 rounded-md text-gray-600 hover:bg-gray-50 transition-colors font-medium text-sm"
          >
            取消
          </button> */}
          <button
            onClick={onRedirect}
            className="px-5 py-2 bg-blue-600 rounded-md text-white hover:bg-blue-700 transition-colors font-medium text-sm flex items-center gap-1"
          >
            立即前往
            <Icon icon="mdi:rocket-launch" className="text-white ml-1" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default LanguageRedirectModal; 