// /**
//  * 安全构建脚本 - 逐步执行Next.js静态导出，避免博客页面生成导致的卡顿问题
//  */
// const { execSync } = require('child_process');
// const fs = require('fs');
// const path = require('path');

// console.log('======== 开始执行安全构建流程 ========');
// console.time('总构建时间');

// try {
//   // 步骤1: 确保临时目录存在
//   console.log('1. 检查和创建临时目录...');
//   const tempDir = path.join(__dirname, '..', 'temp');
//   if (!fs.existsSync(tempDir)) {
//     fs.mkdirSync(tempDir, { recursive: true });
//   }
  
//   // 首先备份原始配置文件
//   console.log('2. 备份原始Next.js配置...');
//   const originalConfigPath = path.join(__dirname, '..', 'next.config.js');
//   const tempConfigPath = path.join(tempDir, 'next.config.backup.js');
  
//   fs.copyFileSync(originalConfigPath, tempConfigPath);
  
//   try {
//     // 步骤2: 使用简化配置构建非博客页面
//     console.log('3. 将简化配置复制到next.config.js...');
//     const simpleConfigPath = path.join(__dirname, '..', 'next.config.simple.js');
//     fs.copyFileSync(simpleConfigPath, originalConfigPath);
    
//     console.log('4. 使用简化配置构建非博客页面...');
//     execSync('next build', { 
//       stdio: 'inherit',
//       cwd: path.join(__dirname, '..')
//     });
    
//   } finally {
//     // 还原原始配置
//     console.log('5. 还原原始Next.js配置...');
//     fs.copyFileSync(tempConfigPath, originalConfigPath);
//   }
  
//   // 步骤3: 执行post-build脚本
//   console.log('6. 执行post-build处理...');
//   execSync('node post-build.js', { 
//     stdio: 'inherit',
//     cwd: path.join(__dirname, '..')
//   });
  
//   // 步骤4: 验证输出目录
//   console.log('7. 验证输出目录结构...');
//   const outDir = path.join(__dirname, '..', 'out');
//   if (!fs.existsSync(outDir)) {
//     throw new Error('输出目录不存在，构建可能失败');
//   }
  
//   // 用于博客详情页的HTML模板
//   const blogPageTemplate = `<!DOCTYPE html>
// <html>
// <head>
//   <meta charset="utf-8">
//   <meta name="viewport" content="width=device-width, initial-scale=1.0">
//   <title>博客页面 - DeepBI</title>
//   <script>
//     // 当DOM加载完成时，获取当前URL参数并处理重定向
//     window.addEventListener('DOMContentLoaded', function() {
//       const pathSegments = window.location.pathname.split('/');
//       const localeIndex = pathSegments.indexOf('blog') - 1;
//       const locale = localeIndex >= 0 ? pathSegments[localeIndex] : 'zh';
      
//       // 如果是详情页路径，提取slug
//       let slug = '';
//       if (pathSegments.length > pathSegments.indexOf('blog') + 1) {
//         slug = pathSegments[pathSegments.indexOf('blog') + 1];
//       }
      
//       // 构建重定向URL
//       let redirectURL = '/' + locale + '/blog';
//       if (slug) {
//         redirectURL += '?slug=' + slug;
//       }
      
//       // 设置重定向链接
//       document.getElementById('redirectLink').href = redirectURL;
      
//       // 重定向到带查询参数的博客页面
//       window.location.href = redirectURL;
//     });
//   </script>
// </head>
// <body>
//   <div style="display: flex; justify-content: center; align-items: center; height: 100vh; font-family: Arial, sans-serif;">
//     <div style="text-align: center;">
//       <h1>正在加载博客...</h1>
//       <p>如果页面没有自动跳转，请<a id="redirectLink" href="#">点击这里</a></p>
//     </div>
//   </div>
// </body>
// </html>`;

//   // 步骤5: 为每种语言创建博客页面
//   console.log('8. 为每种语言创建博客页面...');
//   const locales = ['zh', 'en', 'ja'];
//   locales.forEach(locale => {
//     const localeBlogDir = path.join(outDir, locale, 'blog');
    
//     // 创建博客主页
//     if (!fs.existsSync(localeBlogDir)) {
//       fs.mkdirSync(localeBlogDir, { recursive: true });
//     }
    
//     // 写入博客列表页的HTML
//     fs.writeFileSync(
//       path.join(localeBlogDir, 'index.html'),
//       blogPageTemplate
//     );
    
//     console.log(`   创建 ${locale} 语言的博客页面完成`);
//   });
  
//   // 步骤6: 确认构建完成
//   console.log('9. 构建完成，验证输出目录...');
//   const files = fs.readdirSync(outDir);
//   console.log(`   输出目录中有 ${files.length} 个文件/文件夹`);
  
//   console.timeEnd('总构建时间');
//   console.log('======== 安全构建流程执行完毕 ========');
  
// } catch (error) {
//   console.error('构建过程中发生错误:', error);
//   process.exit(1);
// } 