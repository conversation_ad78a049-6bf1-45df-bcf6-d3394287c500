# 部署指南

## 架构说明

```
域名根目录 (/)
├── 主站文件 (out/ 目录内容)
└── /docs → 文档站文件 (my-app/out/ 目录内容)
```

## 构建步骤

### 1. 构建项目
```bash
npm run build
```
这会自动构建主站和文档站，生成以下目录结构：
```
out/
├── index.html          # 主站首页
├── blog/              # 博客页面
├── case/              # 案例页面
├── _next/             # 主站静态资源
└── docs/              # 文档站完整内容
    ├── index.html     # 文档首页
    ├── _next/         # 文档站静态资源
    └── ...
```

### 2. 部署到服务器
将 `out/` 目录的内容上传到服务器的网站根目录：
```bash
# 示例：使用 rsync 部署
rsync -avz --delete out/ user@server:/var/www/your-site/
```

### 3. 配置 nginx
参考 `nginx.conf.example` 文件配置 nginx。

## 开发环境

### 主站开发
```bash
npm run dev
# 访问: http://localhost:3000
```

### 文档站独立开发
```bash
cd my-app
npm run dev
# 访问: http://localhost:3000/docs
```

### 测试构建结果
```bash
npm run build
npx serve out -p 8080
# 访问: http://localhost:8080
# 文档: http://localhost:8080/docs
```

## 目录结构

```
项目根目录/
├── app/                # 主站源码 (Next.js App Router)
├── my-app/             # 文档站源码 (Fumadocs)
├── out/                # 构建输出 (部署此目录)
├── scripts/            # 构建脚本
├── nginx.conf.example  # nginx 配置示例
└── DEPLOYMENT.md       # 本文件
```

## 注意事项

1. **路径一致性**: 文档站配置了 `basePath: '/docs'`，确保所有链接都是相对路径
2. **静态资源**: 两个项目的静态资源路径不冲突
3. **SEO**: 主站和文档站都有独立的 sitemap 和 robots.txt
4. **缓存**: nginx 配置了适当的静态资源缓存策略
