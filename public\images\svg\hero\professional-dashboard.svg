<svg width="1200" height="800" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="card-bg" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stop-color="#ffffff" />
          <stop offset="100%" stop-color="#f8fafc" />
        </linearGradient>
        <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
          <feDropShadow dx="0" dy="4" stdDeviation="10" flood-opacity="0.15"/>
        </filter>
        <linearGradient id="chart-blue" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stop-color="#60a5fa" stop-opacity="0.2" />
          <stop offset="100%" stop-color="#60a5fa" stop-opacity="0" />
        </linearGradient>
        <linearGradient id="chart-green" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stop-color="#34d399" stop-opacity="0.2" />
          <stop offset="100%" stop-color="#34d399" stop-opacity="0" />
        </linearGradient>
      </defs>
      
      <!-- 主面板背景 -->
      <rect x="0" y="0" width="1200" height="800" rx="16" fill="#ffffff" filter="url(#shadow)" />
      
      <!-- 顶部导航 -->
      <rect x="0" y="0" width="1200" height="60" rx="16 16 0 0" fill="#f8fafc" />
      <text x="30" y="38" font-family="Arial" font-size="20" font-weight="bold" fill="#1e40af">DeepBI Dashboard</text>
      
      <!-- 状态指示器 -->
      <circle cx="1120" cy="30" r="8" fill="#34d399" />
      <text x="1140" y="38" font-family="Arial" font-size="14" fill="#64748b">AI Powered</text>
      
      <!-- 左侧边栏 -->
      <rect x="0" y="60" width="220" height="740" fill="#f8fafc" />
      
      <!-- 侧边栏菜单 -->
      <g>
        <rect x="20" y="90" width="180" height="40" rx="8" fill="#dbeafe" />
        <text x="50" y="115" font-family="Arial" font-size="14" font-weight="bold" fill="#1e40af">Dashboard</text>
        
        <text x="50" y="170" font-family="Arial" font-size="14" fill="#64748b">Campaigns</text>
        <text x="50" y="220" font-family="Arial" font-size="14" fill="#64748b">Keywords</text>
        <text x="50" y="270" font-family="Arial" font-size="14" fill="#64748b">Analytics</text>
        <text x="50" y="320" font-family="Arial" font-size="14" fill="#64748b">Settings</text>
      </g>
      
      <!-- 主要数据卡片 -->
      <g>
        <!-- ACOS卡片 -->
        <rect x="240" y="80" width="300" height="180" rx="12" fill="url(#card-bg)" stroke="#e2e8f0" stroke-width="1" />
        <text x="270" y="120" font-family="Arial" font-size="16" fill="#64748b">ACOS</text>
        <text x="270" y="170" font-family="Arial" font-size="36" font-weight="bold" fill="#0f172a">14.2%</text>
        <text x="270" y="200" font-family="Arial" font-size="14" fill="#22c55e">↓ 3.5% from last week</text>
        
        <!-- Spend卡片 -->
        <rect x="560" y="80" width="300" height="180" rx="12" fill="url(#card-bg)" stroke="#e2e8f0" stroke-width="1" />
        <text x="590" y="120" font-family="Arial" font-size="16" fill="#64748b">Spend</text>
        <text x="590" y="170" font-family="Arial" font-size="36" font-weight="bold" fill="#0f172a">$1,245</text>
        <text x="590" y="200" font-family="Arial" font-size="14" fill="#ef4444">↑ 5.2% from last week</text>
        
        <!-- Impressions卡片 -->
        <rect x="880" y="80" width="300" height="180" rx="12" fill="url(#card-bg)" stroke="#e2e8f0" stroke-width="1" />
        <text x="910" y="120" font-family="Arial" font-size="16" fill="#64748b">Impressions</text>
        <text x="910" y="170" font-family="Arial" font-size="36" font-weight="bold" fill="#0f172a">52.4K</text>
        <text x="910" y="200" font-family="Arial" font-size="14" fill="#22c55e">↑ 12.8% from last week</text>
        
        <!-- ACOS趋势图 -->
        <rect x="240" y="280" width="460" height="300" rx="12" fill="url(#card-bg)" stroke="#e2e8f0" stroke-width="1" />
        <text x="270" y="320" font-family="Arial" font-size="16" font-weight="bold" fill="#0f172a">ACOS Performance</text>
        
        <path d="M270,500 L670,500" stroke="#e2e8f0" stroke-width="1" />
        <path d="M270,350 L270,500" stroke="#e2e8f0" stroke-width="1" />
        
        <!-- 数据线 -->
        <path d="M270,480 L310,470 L350,450 L390,460 L430,430 L470,400 L510,390 L550,370 L590,380 L630,360 L670,370" stroke="#3b82f6" stroke-width="2.5" fill="none" />
        <path d="M270,480 L670,480 L670,500 L270,500 Z" fill="url(#chart-blue)" />
        
        <!-- X轴标签 -->
        <text x="270" y="520" font-family="Arial" font-size="12" fill="#64748b">Jan</text>
        <text x="350" y="520" font-family="Arial" font-size="12" fill="#64748b">Mar</text>
        <text x="430" y="520" font-family="Arial" font-size="12" fill="#64748b">May</text>
        <text x="510" y="520" font-family="Arial" font-size="12" fill="#64748b">Jul</text>
        <text x="590" y="520" font-family="Arial" font-size="12" fill="#64748b">Sep</text>
        <text x="670" y="520" font-family="Arial" font-size="12" fill="#64748b">Nov</text>
        
        <!-- 销售趋势图 -->
        <rect x="720" y="280" width="460" height="300" rx="12" fill="url(#card-bg)" stroke="#e2e8f0" stroke-width="1" />
        <text x="750" y="320" font-family="Arial" font-size="16" font-weight="bold" fill="#0f172a">Sales Growth</text>
        
        <path d="M750,500 L1150,500" stroke="#e2e8f0" stroke-width="1" />
        <path d="M750,350 L750,500" stroke="#e2e8f0" stroke-width="1" />
        
        <!-- 数据线 -->
        <path d="M750,480 L790,475 L830,470 L870,450 L910,430 L950,410 L990,400 L1030,380 L1070,360 L1110,340 L1150,330" stroke="#22c55e" stroke-width="2.5" fill="none" />
        <path d="M750,480 L1150,330 L1150,500 L750,500 Z" fill="url(#chart-green)" />
        
        <!-- X轴标签 -->
        <text x="750" y="520" font-family="Arial" font-size="12" fill="#64748b">Jan</text>
        <text x="830" y="520" font-family="Arial" font-size="12" fill="#64748b">Mar</text>
        <text x="910" y="520" font-family="Arial" font-size="12" fill="#64748b">May</text>
        <text x="990" y="520" font-family="Arial" font-size="12" fill="#64748b">Jul</text>
        <text x="1070" y="520" font-family="Arial" font-size="12" fill="#64748b">Sep</text>
        <text x="1150" y="520" font-family="Arial" font-size="12" fill="#64748b">Nov</text>
        
        <!-- AI建议卡片 -->
        <rect x="240" y="600" width="940" height="180" rx="12" fill="#f0f9ff" stroke="#bfdbfe" stroke-width="1" />
        <text x="270" y="640" font-family="Arial" font-size="16" font-weight="bold" fill="#0f172a">AI Recommendations</text>
        
        <rect x="260" y="660" width="6" height="6" rx="3" fill="#3b82f6" />
        <text x="280" y="667" font-family="Arial" font-size="14" fill="#334155">Increase bids on "amazon ppc tool" by 15% to capture more top positions</text>
        
        <rect x="260" y="690" width="6" height="6" rx="3" fill="#3b82f6" />
        <text x="280" y="697" font-family="Arial" font-size="14" fill="#334155">Add negative keyword "free" to campaigns to improve relevance</text>
        
        <rect x="260" y="720" width="6" height="6" rx="3" fill="#3b82f6" />
        <text x="280" y="727" font-family="Arial" font-size="14" fill="#334155">Shift 20% of budget from auto campaigns to keyword-targeted campaigns</text>
        
        <rect x="260" y="750" width="6" height="6" rx="3" fill="#3b82f6" />
        <text x="280" y="757" font-family="Arial" font-size="14" fill="#334155">Focus on increasing CTR for top 5 keywords to improve quality score</text>
      </g>
    </svg>