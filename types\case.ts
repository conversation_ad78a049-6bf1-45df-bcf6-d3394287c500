export type CaseAuthor = {
  name: string;
  image: string;
  designation: string;
};

export type CaseSection = {
  id: string;
  title: string;
  content: string; // HTML内容
  level: number; // 标题级别 (1-6)
};

export type CaseContent = {
  content: string; // HTML格式的案例正文
  excerpt: string; // 摘要
  metaTitle?: string;
  metaDescription?: string;
  sections?: CaseSection[];
  tableOfContents?: boolean;
  relatedCases?: number[];
  readingTime?: number;
  status: 'draft' | 'published' | 'archived';
  publishDate: string;
  lastModified?: string;
};

export type CaseItem = {
  id: number;
  title: string;
  paragraph: string;
  image: string;
  author: CaseAuthor;
  tags: string[];
  publishDate: string;
  content?: CaseContent;
};



