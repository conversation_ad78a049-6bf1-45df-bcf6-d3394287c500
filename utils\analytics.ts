// GA4 事件追踪工具函数

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

// 检查 gtag 是否可用
const isGtagAvailable = (): boolean => {
  return typeof window !== 'undefined' && typeof window.gtag === 'function';
};

// 博客点击事件
export const trackBlogClick = (blogId: string | number, blogTitle: string, source: string = 'blog_list') => {
  if (!isGtagAvailable()) {
    console.warn('GA4 gtag not available');
    return;
  }

  window.gtag('event', 'blog_click', {
    event_category: 'Blog',
    event_label: blogTitle,
    blog_id: blogId,
    blog_title: blogTitle,
    click_source: source, // 'blog_list', 'related_posts', 'header_menu' 等
    custom_parameter_1: `blog_${blogId}`,
  });

  console.log('GA4 Blog Click Event:', {
    blog_id: blogId,
    blog_title: blogTitle,
    click_source: source
  });
};

// 博客页面浏览事件
export const trackBlogView = (blogId: string | number, blogTitle: string) => {
  if (!isGtagAvailable()) {
    console.warn('GA4 gtag not available');
    return;
  }

  window.gtag('event', 'blog_view', {
    event_category: 'Blog',
    event_label: blogTitle,
    blog_id: blogId,
    blog_title: blogTitle,
    page_title: blogTitle,
  });

  console.log('GA4 Blog View Event:', {
    blog_id: blogId,
    blog_title: blogTitle
  });
};

// 博客阅读时长事件（可选）
export const trackBlogReadTime = (blogId: string | number, blogTitle: string, readTimeSeconds: number) => {
  if (!isGtagAvailable()) {
    console.warn('GA4 gtag not available');
    return;
  }

  window.gtag('event', 'blog_read_time', {
    event_category: 'Blog',
    event_label: blogTitle,
    blog_id: blogId,
    blog_title: blogTitle,
    read_time_seconds: readTimeSeconds,
    engagement_time_msec: readTimeSeconds * 1000,
  });

  console.log('GA4 Blog Read Time Event:', {
    blog_id: blogId,
    blog_title: blogTitle,
    read_time_seconds: readTimeSeconds
  });
};

// Case点击事件
export const trackCaseClick = (caseId: string | number, caseTitle: string, source: string = 'case_list') => {
  if (!isGtagAvailable()) {
    console.warn('GA4 gtag not available');
    return;
  }

  window.gtag('event', 'case_click', {
    event_category: 'Case',
    event_label: caseTitle,
    case_id: caseId,
    case_title: caseTitle,
    click_source: source, // 'case_list', 'related_cases', 'header_menu' 等
    custom_parameter_1: `case_${caseId}`,
  });

  console.log('GA4 Case Click Event:', {
    case_id: caseId,
    case_title: caseTitle,
    click_source: source
  });
};

// Case页面浏览事件
export const trackCaseView = (caseId: string | number, caseTitle: string) => {
  if (!isGtagAvailable()) {
    console.warn('GA4 gtag not available');
    return;
  }

  window.gtag('event', 'case_view', {
    event_category: 'Case',
    event_label: caseTitle,
    case_id: caseId,
    case_title: caseTitle,
    page_title: caseTitle,
  });

  console.log('GA4 Case View Event:', {
    case_id: caseId,
    case_title: caseTitle
  });
};

// 通用事件追踪函数
export const trackEvent = (eventName: string, parameters: Record<string, any> = {}) => {
  if (!isGtagAvailable()) {
    console.warn('GA4 gtag not available');
    return;
  }

  window.gtag('event', eventName, parameters);
  console.log(`GA4 Event: ${eventName}`, parameters);
};
