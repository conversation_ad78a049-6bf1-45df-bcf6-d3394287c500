<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="budget-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stop-color="#facc15" />
          <stop offset="100%" stop-color="#fef08a" />
        </linearGradient>
        <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="#facc15" flood-opacity="0.2"/>
        </filter>
      </defs>
      
      <!-- Background waves -->
      <path d="M0,250 Q150,150 300,250 T600,250" fill="#fffbeb" opacity="0.4"/>
      <path d="M0,280 Q150,180 300,280 T600,280" fill="#fffbeb" opacity="0.6"/>

      <!-- Main budget visualization -->
      <rect x="50" y="70" width="500" height="260" rx="15" fill="#ffffff" filter="url(#shadow)"/>
      
      <!-- Budget circles -->
      <g transform="translate(100, 100)">
        <circle cx="0" cy="0" r="60" fill="#facc15"/>
        <text x="0" y="5" text-anchor="middle" font-family="Arial" font-size="24" font-weight="bold" fill="#ffffff">$500</text>
        <text x="0" y="30" text-anchor="middle" font-family="Arial" font-size="14" fill="#ffffff">Campaign A</text>
      </g>
      
      <g transform="translate(300, 100)">
        <circle cx="0" cy="0" r="40" fill="#facc15"/>
        <text x="0" y="5" text-anchor="middle" font-family="Arial" font-size="18" font-weight="bold" fill="#ffffff">$200</text>
        <text x="0" y="25" text-anchor="middle" font-family="Arial" font-size="12" fill="#ffffff">Campaign B</text>
      </g>

      <g transform="translate(500, 100)">
        <circle cx="0" cy="0" r="30" fill="#facc15"/>
        <text x="0" y="5" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold" fill="#ffffff">$100</text>
        <text x="0" y="20" text-anchor="middle" font-family="Arial" font-size="10" fill="#ffffff">Campaign C</text>
      </g>
      
      <!-- Arrows indicating budget flow -->
      <path d="M160,100 L240,100" stroke="#facc15" stroke-width="4" marker-end="url(#arrowhead)" />
      <path d="M340,100 L470,100" stroke="#facc15" stroke-width="4" marker-end="url(#arrowhead)" />
      
      <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
        <polygon points="0 0, 10 3.5, 0 7" fill="#facc15" />
      </marker>

      <!-- Optimization text -->
      <text x="300" y="250" text-anchor="middle" font-family="Arial" font-size="20" font-weight="bold" fill="#0f172a">AI Smart Allocation</text>
      <text x="300" y="280" text-anchor="middle" font-family="Arial" font-size="16" fill="#64748b">Maximize ROI across campaigns</text>
    </svg>