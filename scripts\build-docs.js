const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

function removeDirSync(targetPath) {
  if (!fs.existsSync(targetPath)) return;
  for (const entry of fs.readdirSync(targetPath)) {
    const fullPath = path.join(targetPath, entry);
    const stat = fs.lstatSync(fullPath);
    if (stat.isDirectory()) {
      removeDirSync(fullPath);
    } else {
      fs.unlinkSync(fullPath);
    }
  }
  fs.rmdirSync(targetPath);
}

function copyDirSync(src, dest) {
  if (!fs.existsSync(src)) return;
  if (!fs.existsSync(dest)) fs.mkdirSync(dest, { recursive: true });
  for (const entry of fs.readdirSync(src)) {
    const srcPath = path.join(src, entry);
    const destPath = path.join(dest, entry);
    const stat = fs.lstatSync(srcPath);
    if (stat.isDirectory()) {
      copyDirSync(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

function copyDirSyncWithConflictResolution(src, dest, isRootLevel = false) {
  if (!fs.existsSync(src)) return;
  if (!fs.existsSync(dest)) fs.mkdirSync(dest, { recursive: true });
  
  // 只重命名根目录下的冲突文件，保持子目录结构不变
  const rootConflictFiles = {
    'favicon.ico': 'docs-favicon.ico'
  };
  
  for (const entry of fs.readdirSync(src)) {
    const srcPath = path.join(src, entry);
    let destPath = path.join(dest, entry);
    const stat = fs.lstatSync(srcPath);
    
    // 只在根目录重命名冲突文件，保持 index.html 和 404.html 不变
    if (!stat.isDirectory() && rootConflictFiles[entry] && isRootLevel) {
      destPath = path.join(dest, rootConflictFiles[entry]);
      console.log(`   重命名根目录冲突文件: ${entry} -> ${rootConflictFiles[entry]}`);
    }
    
    if (stat.isDirectory()) {
      copyDirSyncWithConflictResolution(srcPath, destPath, false);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

function main() {
  const rootDir = path.join(__dirname, '..');
  const myAppDir = path.join(rootDir, 'my-app');
  const myAppOutDir = path.join(myAppDir, 'out');
  const rootOutDir = path.join(rootDir, 'out');
  const targetDocsDir = path.join(rootOutDir, 'docs');

  console.log('======== 构建并集成文档站 (my-app) 到 /docs ========');

  if (!fs.existsSync(myAppDir)) {
    console.log('未找到 my-app 目录，跳过文档构建');
    return;
  }

  try {
    console.log('1) 在 my-app 中执行构建...');
    execSync('npm run build', { cwd: myAppDir, stdio: 'inherit' });
  } catch (e) {
    console.error('my-app 构建失败:', e.message);
    process.exit(1);
  }

  // 确保主站 out 目录存在
  if (!fs.existsSync(rootOutDir)) {
    fs.mkdirSync(rootOutDir, { recursive: true });
  }

  // 处理拷贝：优先拷贝 my-app/out/docs，其次拷贝 my-app/out 根下文件
  const myAppDocsSubDir = path.join(myAppOutDir, 'docs');

  console.log('2) 清理旧的 out/docs 目录...');
  if (fs.existsSync(targetDocsDir)) {
    removeDirSync(targetDocsDir);
  }
  fs.mkdirSync(targetDocsDir, { recursive: true });

  console.log('3) 复制 my-app 导出内容到 out/docs ...');
  // 直接将整个 my-app/out 目录复制到 out/docs，但重命名冲突文件
  copyDirSyncWithConflictResolution(myAppOutDir, targetDocsDir, true);

  // 创建正确的 index.html 重定向到 docs 子目录
  const docsIndexPath = path.join(targetDocsDir, 'index.html');
  const docsSubIndexPath = path.join(targetDocsDir, 'docs', 'index.html');

  if (fs.existsSync(docsSubIndexPath) && !fs.existsSync(docsIndexPath)) {
    console.log('4) 创建 docs 根目录的 index.html...');
    fs.copyFileSync(docsSubIndexPath, docsIndexPath);
  }

  console.log('5) 文档站文件复制完成');
  
  console.log('6) 文档集成完成 → 现在可通过 /docs 访问');
}

main();


