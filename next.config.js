/** @type {import('next').NextConfig} */
const nextConfig = {
    output: 'export',
    trailingSlash: true,
    images: {
        unoptimized: true,
        domains: ['www.deepbi.cn'],
        remotePatterns: [
          {
            protocol: 'https',
            hostname: 'www.deepbi.cn',
            pathname: '/**',
          },
        ],
    },
    // 构建主站时忽略类型检查与 ESLint（my-app 单独构建校验）
    typescript: {
      ignoreBuildErrors: true,
    },
    eslint: {
      ignoreDuringBuilds: true,
    },
    // 使用实验性功能
    experimental: {
      // App目录下不能同时使用clientRouting和exportPathMap
      // clientRouting: true,
    },



    // 移除所有多语言相关配置，网站只支持中文
}

module.exports = nextConfig 