'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { trackEvent } from '@/utils/analytics';
export default function Header() {
  const [isOpen, setIsOpen] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const pathname = usePathname();
  
  // 检查是否应该显示Header - 只有404页面不显示
  const isNotFoundPage = pathname === '/not-found' || pathname.includes('404');
  
 
  // 监听滚动事件，添加背景色变化效果
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  // 只在404页面不显示Header
  if (isNotFoundPage) return null;

  // 菜单配置 - 使用锚点链接
  const menu = [
    { href: '#home', label: 'Home' },
    // { href: '#who-its-for', label: 'Who It\'s For' },
    // { href: '#what-deepbi-does', label: 'Features' },
    // { href: '#how-it-works', label: 'How It Works' },
    // { href: '#results', label: 'Results' },
    // { href: '#trust', label: 'Trust' },
    { href: '/case', label: 'Cases', isExternal: true },
    { href: '/blog', label: 'Blog', isExternal: true },
    // { href: '/help', label: 'Help Center', isExternal: true },
  ];

  // 平滑滚动到锚点
  const scrollToAnchor = (e: React.MouseEvent<HTMLAnchorElement>, href: string, isExternal: boolean = false) => {
    if (isExternal) return; // 如果是外部链接，不执行锚点滚动逻辑

    e.preventDefault();
    const targetId = href.replace('#', '');

    // 如果不在首页，先跳转到首页再滚动
    if (pathname !== '/') {
      window.location.href = `/${href}`;
      return;
    }

    // 在首页时直接滚动到对应section
    const element = document.getElementById(targetId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
    setIsOpen(false); // 点击后关闭移动端菜单
  };

  // Try It Now 按钮点击处理
  const handleTryItNowClick = () => {
    // 如果不在首页，先跳转到首页再滚动
    if (pathname !== '/') {
      window.location.href = '/#try-it-now';
      return;
    }

    // 在首页时直接滚动到 try-it-now section
    const element = document.getElementById('try-it-now');
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
    setIsOpen(false); // 点击后关闭移动端菜单
  };

        return (
        <header className={`fixed top-0 left-0 w-full z-50 transition-all duration-500 ${isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100' : 'bg-transparent'}`}>
      <div className="container mx-auto px-4 md:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/" className="flex items-center relative group">
              <div className="flex items-center transition-transform duration-300">
                <div className="relative w-10 h-10 mr-3">
                  <Image 
                    src="/images/Logo/Deepinsight.png" 
                    alt="DeepBI Logo" 
                    fill
                    className="object-contain"
                  />
                </div>
                <span className={`font-bold text-base transition-colors duration-300 text-[#1838E3] font-heading`}>
                  DeepBI
                </span>
              </div>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button 
              className={`relative p-3 rounded-lg transition-all duration-300 ${isScrolled ? 'text-gray-700 hover:bg-gray-100' : 'text-gray-900 hover:bg-gray-100'}`}
              onClick={() => setIsOpen(!isOpen)}
              aria-label="Toggle menu"
            >
              <div className="w-6 h-6 relative">
                <span className={`absolute block w-6 h-0.5 bg-current transform transition-all duration-300 ${isOpen ? 'rotate-45 top-3' : 'top-1'}`}></span>
                <span className={`absolute block w-6 h-0.5 bg-current transform transition-all duration-300 ${isOpen ? 'opacity-0' : 'top-3'}`}></span>
                <span className={`absolute block w-6 h-0.5 bg-current transform transition-all duration-300 ${isOpen ? '-rotate-45 top-3' : 'top-5'}`}></span>
              </div>
            </button>
          </div>

          {/* Desktop menu */}
          <div className="hidden md:flex items-center space-x-1 lg:space-x-2">
            {menu.map((item) => (
              item.isExternal ? (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={() => {
                    if (item.label === 'Blog') {
                      trackEvent('blog_menu_click', {
                        event_category: 'Navigation',
                        event_label: 'Header Blog Menu',
                        click_source: 'header_menu'
                      });
                    } else if (item.label === 'Cases') {
                      trackEvent('case_menu_click', {
                        event_category: 'Navigation',
                        event_label: 'Header Cases Menu',
                        click_source: 'header_menu'
                      });
                    }
                  }}
                  className={`relative text-sm lg:text-base transition-all duration-300 px-3 lg:px-4 py-2 rounded-lg group
                    ${isScrolled
                      ? 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                      : 'text-gray-800 hover:text-blue-600 hover:bg-blue-50'
                    }
                    after:absolute after:left-1/2 after:-translate-x-1/2 after:bottom-0 after:h-0.5 after:rounded-full after:bg-blue-500 after:transition-all after:duration-300
                    after:w-0 hover:after:w-6`}
                >
                  {item.label}
                </Link>
              ) : (
                <a
                  key={item.href}
                  href={item.href}
                  onClick={(e) => scrollToAnchor(e, item.href)}
                  className={`relative text-sm lg:text-base transition-all duration-300 px-3 lg:px-4 py-2 rounded-lg group cursor-pointer
                    ${isScrolled 
                      ? 'text-gray-700 hover:text-blue-600 hover:bg-blue-50' 
                      : 'text-gray-800 hover:text-blue-600 hover:bg-blue-50'
                    }
                    after:absolute after:left-1/2 after:-translate-x-1/2 after:bottom-0 after:h-0.5 after:rounded-full after:bg-blue-500 after:transition-all after:duration-300
                    after:w-0 hover:after:w-6`}
                >
                  {item.label}
                </a>
              )
            ))}
          </div>

          <div className="hidden md:flex items-center ml-6 lg:ml-8">
                    <button
              onClick={handleTryItNowClick}
              className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-2.5 rounded-xl text-sm  font-semibold hover:from-blue-700 hover:to-blue-800 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl font-heading"
            >
          Try It Now
        </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isOpen && (
        <div className="md:hidden absolute top-full left-0 right-0 bg-white/95 backdrop-blur-md shadow-xl border-t border-gray-100">
          <div className="container mx-auto px-4 py-6">
            <div className="space-y-1">
              {menu.map((item) => (
                item.isExternal ? (
                  <Link
                    key={item.href}
                    href={item.href}
                    className="block py-3 px-4 text-gray-700 font-semibold text-base rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-all duration-300"
                    onClick={() => {
                      if (item.label === 'Blog') {
                        trackEvent('blog_menu_click', {
                          event_category: 'Navigation',
                          event_label: 'Mobile Blog Menu',
                          click_source: 'mobile_menu'
                        });
                      } else if (item.label === 'Cases') {
                        trackEvent('case_menu_click', {
                          event_category: 'Navigation',
                          event_label: 'Mobile Cases Menu',
                          click_source: 'mobile_menu'
                        });
                      }
                      setIsOpen(false);
                    }}
                  >
                    {item.label}
                  </Link>
                ) : (
                  <a
                    key={item.href}
                    href={item.href}
                    onClick={(e) => scrollToAnchor(e, item.href)}
                    className="block py-3 px-4 text-gray-700 font-semibold text-base rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-all duration-300"
                  >
                    {item.label}
                  </a>
                )
              ))}
            </div>
            {/* <button
              onClick={handleTryItNowClick}
              className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-2 rounded-xl text-base font-semibold hover:from-blue-700 hover:to-blue-800 transition-all duration-300 mt-6 w-full shadow-lg font-heading"
            >
            Try It Now
            </button> */}
          </div>
        </div>
      )}
    </header>
  );
} 