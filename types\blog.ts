export type Author = {
  name: string;
  image: string;
  designation: string;
};

export type BlogContent = {
  // 文章主体内容
  content: string; // HTML格式的文章内容
  excerpt: string; // 文章摘要，用于列表页显示
  
  // SEO相关
  metaTitle?: string; // 自定义SEO标题，如果不提供则使用title
  metaDescription?: string; // 自定义SEO描述，如果不提供则使用excerpt
  
  // 文章结构
  sections?: BlogSection[]; // 文章章节结构
  tableOfContents?: boolean; // 是否显示目录
  
  // 相关文章
  relatedPosts?: number[]; // 相关文章的ID列表
  
  // 阅读时间估算
  readingTime?: number; // 预计阅读时间（分钟）
  
  // 文章状态
  status?: 'draft' | 'published' | 'archived';
  publishDate?: string; // 发布日期
  lastModified?: string; // 最后修改日期
};

export type BlogSection = {
  id: string;
  title: string;
  content: string; // HTML内容
  level: number; // 标题级别 (1-6)
};

export type Blog = {
  id: number;
  title: string;
  paragraph: string; // 保留兼容性，建议使用excerpt
  image: string;
  author: Author;
  tags: string[];
  publishDate: string;
  
  // 新增的完整内容字段
  content?: BlogContent;
};
