'use client';

import React, { useState, useEffect } from 'react';
import LanguageRedirectModal from './LanguageRedirectModal';

const ClientWrapper: React.FC = () => {
  const [showModal, setShowModal] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const chineseWebsiteUrl = "https://www.deepbi.cn"; // 中文网站URL

  useEffect(() => {
    // 确保在客户端环境中运行
    setIsClient(true);
    
    // 检查是否已经展示过弹窗 (使用localStorage避免重复显示)
    try {
      const redirectModalData = localStorage.getItem('hasShownLanguageRedirectModal');
      let shouldShowModal = true;
      
      if (redirectModalData) {
        try {
          // 尝试解析存储的数据
          const data = JSON.parse(redirectModalData);
          const timestamp = data.timestamp;
          const currentTime = new Date().getTime();
          
          // 检查是否在7天有效期内 (7天 = 7 * 24 * 60 * 60 * 1000 毫秒)
          // const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000;
          // if (currentTime - timestamp < sevenDaysInMs) {
          //   shouldShowModal = false;
          // }
        } catch (error) {
          // 如果解析失败，清除旧数据并显示弹窗
          localStorage.removeItem('hasShownLanguageRedirectModal');
        }
      }
      
      if (shouldShowModal) {
        // 检测浏览器语言
        const userLanguages = navigator.languages || [navigator.language];
        
        // 检查是否为中文 (包括zh-CN, zh-TW, zh等)
        const isChinese = userLanguages.some(lang => lang.toLowerCase().startsWith('zh'));
        
        // 检查当前URL是否已经是中文版
        const isAlreadyOnChineseSite = window.location.href.includes(chineseWebsiteUrl);
        
        // 如果是中文，而且不在中文网站上，显示弹窗
        if (isChinese && !isAlreadyOnChineseSite) {
          setShowModal(true);
        }
      }
    } catch (error) {
      // 如果有任何错误，静默处理，不显示弹窗
      console.warn('ClientWrapper error:', error);
    }
  }, []);
  
  // 简单关闭弹窗，不记录到localStorage，下次刷新页面时会再显示
  const handleSimpleClose = () => {
    setShowModal(false);
  };
  
  // 关闭弹窗并设置7天内不再显示
  const handleCloseWithDelay = () => {
    // 记录已经展示过弹窗，避免重复显示，并设置过期时间
    const data = {
      timestamp: new Date().getTime(),
      choice: 'closeWithDelay'
    };
    localStorage.setItem('hasShownLanguageRedirectModal', JSON.stringify(data));
    setShowModal(false);
  };
  
  const handleRedirect = () => {
    // 记录已经展示过弹窗，并记录用户选择了跳转
    const data = {
      timestamp: new Date().getTime(),
      choice: 'redirect'
    };
    localStorage.setItem('hasShownLanguageRedirectModal', JSON.stringify(data));
    // 重定向到中文网站
    window.location.href = chineseWebsiteUrl;
  };
  
  // 只在客户端环境中渲染
  if (!isClient) {
    return null;
  }

  return (
    <LanguageRedirectModal 
      isOpen={showModal} 
      onClose={handleSimpleClose} 
      onCloseWithDelay={handleCloseWithDelay}
      onRedirect={handleRedirect} 
    />
  );
};

export default ClientWrapper; 