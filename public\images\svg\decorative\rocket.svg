<svg width="120" height="240" viewBox="0 0 120 240" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="rocketBody" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stop-color="#2563eb" />
          <stop offset="100%" stop-color="#1d4ed8" />
        </linearGradient>
        <linearGradient id="flame" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" stop-color="#ef4444" />
          <stop offset="50%" stop-color="#f97316" />
          <stop offset="100%" stop-color="#fde047" />
        </linearGradient>
        <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur in="SourceGraphic" stdDeviation="5" result="blur" />
          <feColorMatrix in="blur" type="matrix" values="1 0 0 0 1  0 1 0 0 0.5  0 0 1 0 0  0 0 0 18 -7" result="glow" />
          <feBlend in="SourceGraphic" in2="glow" mode="normal" />
        </filter>
      </defs>
      <!-- 火箭主体 -->
      <path d="M60 20 L80 80 L80 160 C80 180 60 180 60 180 C60 180 40 180 40 160 L40 80 Z" fill="url(#rocketBody)" stroke="#0f172a" stroke-width="2" />
      <ellipse cx="60" cy="20" rx="20" ry="30" fill="url(#rocketBody)" stroke="#0f172a" stroke-width="2" />
      
      <!-- 窗口 -->
      <circle cx="60" cy="70" r="10" fill="#bfdbfe" stroke="#0f172a" stroke-width="1" />
      
      <!-- 火箭翼 -->
      <path d="M40 120 L20 140 L40 140 Z" fill="#1d4ed8" stroke="#0f172a" stroke-width="2" />
      <path d="M80 120 L100 140 L80 140 Z" fill="#1d4ed8" stroke="#0f172a" stroke-width="2" />
      
      <!-- 火焰 -->
      <g filter="url(#glow)">
        <path d="M50 180 Q60 220 70 180" fill="url(#flame)" />
        <path d="M52 180 Q60 230 68 180" fill="url(#flame)" opacity="0.7" />
      </g>
      
      <!-- 星星 -->
      <circle cx="30" cy="40" r="2" fill="white" />
      <circle cx="100" cy="50" r="1.5" fill="white" />
      <circle cx="20" cy="90" r="1" fill="white" />
      <circle cx="90" cy="110" r="1.2" fill="white" />
    </svg>